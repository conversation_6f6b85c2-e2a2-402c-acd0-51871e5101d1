<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮层面板测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .section h2 {
            color: #34495e;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 15px 0;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }

        .status-display {
            background: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
        }

        .floating-panel-info {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .floating-panel-info h3 {
            color: #27ae60;
            margin-top: 0;
        }

        .list-group {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .list-group-item {
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            border-bottom: none;
            background: white;
        }

        .list-group-item:first-child {
            border-radius: 4px 4px 0 0;
        }

        .list-group-item:last-child {
            border-bottom: 1px solid #e0e0e0;
            border-radius: 0 0 4px 4px;
        }

        .list-group-item:hover {
            background: #f8f9fa;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            background: #6c757d;
            color: white;
        }

        .badge-success {
            background: #28a745;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-danger {
            background: #dc3545;
        }

        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 500;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 浮层自动化面板测试</h1>
        
        <div class="floating-panel-info">
            <h3>📋 使用说明</h3>
            <p>此页面用于测试浮层自动化面板功能。安装插件后，您应该能看到页面左侧的浮动触发按钮。</p>
            <ul>
                <li>点击左侧的浮动按钮打开自动化面板</li>
                <li>在面板中选择工作流并执行</li>
                <li>使用下方的测试元素验证自动化功能</li>
            </ul>
            <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                <h4 style="margin-top: 0; color: #856404;">🔧 调试工具</h4>
                <p style="margin-bottom: 10px; font-size: 14px;">如果左侧没有显示浮动按钮，请尝试以下操作：</p>
                <button onclick="forceInitFloating()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">强制初始化浮层</button>
                <button onclick="checkFloatingStatus()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">检查状态</button>
                <button onclick="testFloatingPanel()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">测试浮层面板</button>
                <button onclick="openConsole()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">打开控制台</button>
                <div id="debugInfo" style="font-family: monospace; font-size: 12px; margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 3px;">
                    等待插件加载...
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 测试元素区域</h2>
            
            <div class="form-group">
                <label for="testInput">测试输入框</label>
                <input type="text" id="testInput" class="form-control" placeholder="这里可以测试输入操作">
            </div>

            <div class="form-group">
                <label for="testSelect">测试下拉选择</label>
                <select id="testSelect" class="form-control">
                    <option value="">请选择...</option>
                    <option value="option1">选项 1</option>
                    <option value="option2">选项 2</option>
                    <option value="option3">选项 3</option>
                </select>
            </div>

            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="check1">
                    <label for="check1">复选框 1</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="check2">
                    <label for="check2">复选框 2</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="check3">
                    <label for="check3">复选框 3</label>
                </div>
            </div>

            <div class="test-buttons">
                <button class="btn btn-primary" id="testBtn1">测试按钮 1</button>
                <button class="btn btn-success" id="testBtn2">测试按钮 2</button>
                <button class="btn btn-warning" id="testBtn3">测试按钮 3</button>
                <button class="btn btn-danger" id="testBtn4">测试按钮 4</button>
            </div>
        </div>

        <div class="section">
            <h2>📊 状态监控</h2>
            <div id="statusDisplay" class="status-display">
                等待操作...
            </div>
            
            <div class="progress">
                <div id="progressBar" class="progress-bar" style="width: 0%;">0%</div>
            </div>
        </div>

        <div class="section">
            <h2>📝 操作日志</h2>
            <ul id="actionLog" class="list-group">
                <li class="list-group-item">
                    <span class="badge badge-success">INFO</span>
                    页面加载完成 - <small>等待用户操作</small>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // 测试页面交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const statusDisplay = document.getElementById('statusDisplay');
            const progressBar = document.getElementById('progressBar');
            const actionLog = document.getElementById('actionLog');

            function addLog(message, type = 'info') {
                const logItem = document.createElement('li');
                logItem.className = 'list-group-item';
                
                const badge = document.createElement('span');
                badge.className = `badge badge-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'success'}`;
                badge.textContent = type.toUpperCase();
                
                const time = new Date().toLocaleTimeString();
                logItem.innerHTML = `${badge.outerHTML} ${message} - <small>${time}</small>`;
                
                actionLog.insertBefore(logItem, actionLog.firstChild);
                
                // 限制日志条数
                if (actionLog.children.length > 10) {
                    actionLog.removeChild(actionLog.lastChild);
                }
            }

            function updateStatus(message) {
                statusDisplay.textContent = message;
                addLog(message);
            }

            // 为测试按钮添加事件监听
            document.querySelectorAll('.btn').forEach((btn, index) => {
                btn.addEventListener('click', function() {
                    updateStatus(`点击了 ${this.textContent}`);
                    
                    // 模拟进度
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += 10;
                        progressBar.style.width = progress + '%';
                        progressBar.textContent = progress + '%';
                        
                        if (progress >= 100) {
                            clearInterval(interval);
                            setTimeout(() => {
                                progressBar.style.width = '0%';
                                progressBar.textContent = '0%';
                            }, 1000);
                        }
                    }, 100);
                });
            });

            // 输入框事件
            document.getElementById('testInput').addEventListener('input', function() {
                updateStatus(`输入框内容: ${this.value}`);
            });

            // 下拉选择事件
            document.getElementById('testSelect').addEventListener('change', function() {
                updateStatus(`选择了: ${this.value || '空值'}`);
            });

            // 复选框事件
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateStatus(`${this.nextElementSibling.textContent} ${this.checked ? '已选中' : '已取消'}`);
                });
            });

            addLog('测试页面初始化完成');

            // 初始化测试数据
            initTestData();

            // 检查浮层状态
            setTimeout(checkFloatingStatus, 3000);
        });

        // 调试函数
        function forceInitFloating() {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.textContent = '正在强制初始化浮层...';

            // 首先初始化测试数据
            initTestData();

            try {
                if (typeof window.forceInitFloatingSystem === 'function') {
                    window.forceInitFloatingSystem();
                    debugInfo.textContent = '已调用强制初始化函数';
                } else if (typeof window.forceInitTrigger === 'function') {
                    window.forceInitTrigger();
                    debugInfo.textContent = '已调用触发器强制初始化函数';
                } else {
                    debugInfo.textContent = '未找到强制初始化函数，请检查插件是否正确加载';
                }
            } catch (error) {
                debugInfo.textContent = '强制初始化失败: ' + error.message;
            }

            // 2秒后检查状态
            setTimeout(checkFloatingStatus, 2000);
        }

        // 初始化测试数据
        function initTestData() {
            // 模拟chrome.storage API
            if (typeof chrome === 'undefined') {
                window.chrome = {};
            }
            if (!chrome.storage) {
                chrome.storage = {
                    local: {
                        get: function(keys, callback) {
                            console.log('模拟chrome.storage.local.get调用:', keys);
                            const testData = {
                                automationWorkflows: [
                                    {
                                        name: '测试工作流 1',
                                        steps: [
                                            { type: 'click', name: '点击测试按钮1', locator: { strategy: 'id', value: 'testBtn1' } }
                                        ]
                                    },
                                    {
                                        name: '测试工作流 2',
                                        steps: [
                                            { type: 'input', name: '输入测试文本', locator: { strategy: 'id', value: 'testInput' } }
                                        ]
                                    }
                                ]
                            };
                            setTimeout(() => callback(testData), 100);
                        },
                        set: function(data, callback) {
                            console.log('模拟chrome.storage.local.set调用:', data);
                            if (callback) setTimeout(callback, 100);
                        }
                    },
                    onChanged: {
                        addListener: function(callback) {
                            console.log('模拟chrome.storage.onChanged.addListener调用');
                        }
                    }
                };
            }

            console.log('✅ 测试数据已初始化');
        }

        function checkFloatingStatus() {
            const debugInfo = document.getElementById('debugInfo');
            const trigger = document.getElementById('automation-floating-trigger');

            let status = '状态检查:\n';
            status += `- 触发器元素: ${trigger ? '✅ 存在' : '❌ 不存在'}\n`;
            status += `- 浮层系统已加载: ${window.floatingAutomationLoaded ? '✅ 是' : '❌ 否'}\n`;
            status += `- 触发器实例: ${window.floatingTriggerInstance ? '✅ 存在' : '❌ 不存在'}\n`;
            status += `- Chrome扩展API: ${typeof chrome !== 'undefined' ? '✅ 可用' : '❌ 不可用'}\n`;
            status += `- Chrome.storage: ${typeof chrome !== 'undefined' && chrome.storage ? '✅ 可用' : '❌ 不可用'}\n`;
            status += `- Chrome.runtime: ${typeof chrome !== 'undefined' && chrome.runtime ? '✅ 可用' : '❌ 不可用'}\n`;
            status += `- 当前URL: ${window.location.href}\n`;
            status += `- FloatingPanel类: ${typeof window.FloatingPanel !== 'undefined' ? '✅ 存在' : '❌ 不存在'}\n`;
            status += `- FloatingTrigger类: ${typeof window.FloatingTrigger !== 'undefined' ? '✅ 存在' : '❌ 不存在'}\n`;

            debugInfo.textContent = status;

            // 如果触发器不存在，尝试手动创建
            if (!trigger && typeof window.FloatingTrigger !== 'undefined') {
                console.log('🔧 触发器不存在，尝试手动创建...');
                try {
                    window.floatingTriggerInstance = new window.FloatingTrigger();
                    setTimeout(() => {
                        const newTrigger = document.getElementById('automation-floating-trigger');
                        if (newTrigger) {
                            debugInfo.textContent += '\n✅ 手动创建触发器成功！';
                        } else {
                            debugInfo.textContent += '\n❌ 手动创建触发器失败';
                        }
                    }, 1000);
                } catch (error) {
                    debugInfo.textContent += `\n❌ 手动创建触发器失败: ${error.message}`;
                }
            }
        }

        function testFloatingPanel() {
            const debugInfo = document.getElementById('debugInfo');

            try {
                if (window.floatingTriggerInstance && window.floatingTriggerInstance.floatingPanel) {
                    // 如果面板已存在，切换显示
                    window.floatingTriggerInstance.floatingPanel.toggle();
                    debugInfo.textContent = '已切换浮层面板显示状态';
                } else if (window.floatingTriggerInstance) {
                    // 如果触发器存在但面板不存在，初始化面板
                    window.floatingTriggerInstance.initFloatingPanel();
                    setTimeout(() => {
                        if (window.floatingTriggerInstance.floatingPanel) {
                            window.floatingTriggerInstance.floatingPanel.show();
                            debugInfo.textContent = '已创建并显示浮层面板';
                        } else {
                            debugInfo.textContent = '创建浮层面板失败';
                        }
                    }, 500);
                } else {
                    debugInfo.textContent = '触发器实例不存在，请先初始化浮层';
                }
            } catch (error) {
                debugInfo.textContent = '测试浮层面板失败: ' + error.message;
                console.error('测试浮层面板失败:', error);
            }
        }

        function openConsole() {
            alert('请按 F12 打开开发者工具，然后查看 Console 标签页的日志信息');
        }
    </script>
</body>
</html>
