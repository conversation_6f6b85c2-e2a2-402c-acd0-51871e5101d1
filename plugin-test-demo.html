<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>自动化插件测试Demo</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(45deg, #3498db, #2ecc71);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }

      .content {
        padding: 40px;
      }

      .section {
        margin-bottom: 40px;
        padding: 30px;
        border: 2px solid #ecf0f1;
        border-radius: 10px;
        background: #fafafa;
      }

      .section h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.8em;
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
      }

      /* 按钮样式 */
      .btn {
        display: inline-block;
        padding: 12px 25px;
        margin: 10px;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .btn-primary {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
      }

      .btn-success {
        background: linear-gradient(45deg, #2ecc71, #27ae60);
        color: white;
      }

      .btn-warning {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
      }

      .btn-danger {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }

      /* 表单样式 */
      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #2c3e50;
      }

      .form-control {
        width: 100%;
        padding: 12px;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
      }

      .form-control:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
      }

      /* 表格样式 */
      .table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .table th,
      .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #ecf0f1;
      }

      .table th {
        background: linear-gradient(45deg, #34495e, #2c3e50);
        color: white;
        font-weight: bold;
      }

      .table tr:hover {
        background: #f8f9fa;
      }

      /* 卡片样式 */
      .card {
        background: white;
        border-radius: 10px;
        padding: 25px;
        margin: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        border: 1px solid #ecf0f1;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .card h3 {
        color: #2c3e50;
        margin-bottom: 15px;
      }

      .card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      /* 列表样式 */
      .list-group {
        list-style: none;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .list-item {
        padding: 15px 20px;
        border-bottom: 1px solid #ecf0f1;
        transition: background 0.3s ease;
        cursor: pointer;
      }

      .list-item:hover {
        background: #f8f9fa;
      }

      .list-item:last-child {
        border-bottom: none;
      }

      /* 状态指示器 */
      .status {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .status-active {
        background: #2ecc71;
        color: white;
      }

      .status-pending {
        background: #f39c12;
        color: white;
      }

      .status-inactive {
        background: #95a5a6;
        color: white;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .content {
          padding: 20px;
        }

        .section {
          padding: 20px;
        }

        .header h1 {
          font-size: 2em;
        }
      }

      /* 动画效果 */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .section {
        animation: fadeIn 0.6s ease-out;
      }

      /* 拖拽区域样式 */
      .drag-area {
        border: 3px dashed #bdc3c7;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background: #ecf0f1;
        transition: all 0.3s ease;
        cursor: move;
      }

      .drag-area:hover {
        border-color: #3498db;
        background: #e8f4f8;
      }

      .draggable-item {
        display: inline-block;
        padding: 15px 25px;
        margin: 10px;
        background: linear-gradient(45deg, #9b59b6, #8e44ad);
        color: white;
        border-radius: 25px;
        cursor: move;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .draggable-item:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }

      /* 表格中的拖拽元素样式 */
      .draggable-handle {
        transition: all 0.3s ease;
        user-select: none;
        position: relative;
        -webkit-user-drag: element;
        -khtml-user-drag: element;
        -moz-user-drag: element;
        -o-user-drag: element;
        user-drag: element;
      }

      .draggable-handle:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      .draggable-handle:active {
        transform: scale(0.95);
      }

      /* 拖拽时的视觉反馈 */
      .dragging {
        opacity: 0.5;
        transform: rotate(5deg);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🚀 自动化插件测试Demo</h1>
        <p>测试各种自动化操作功能的综合演示页面</p>
      </div>

      <div class="content">
        <!-- 按钮测试区域 -->
        <div class="section">
          <h2>🔘 按钮操作测试</h2>
          <p>测试各种类型的按钮点击操作</p>
          <button
            class="btn btn-primary"
            onclick="showAlert('主要按钮被点击!')"
          >
            主要按钮
          </button>
          <button
            class="btn btn-success"
            onclick="showAlert('成功按钮被点击!')"
          >
            成功按钮
          </button>
          <button
            class="btn btn-warning"
            onclick="showAlert('警告按钮被点击!')"
          >
            警告按钮
          </button>
          <button class="btn btn-danger" onclick="showAlert('危险按钮被点击!')">
            危险按钮
          </button>
          <a
            href="#"
            class="btn btn-primary"
            onclick="showAlert('链接按钮被点击!')"
            >链接按钮</a
          >
        </div>

        <!-- 表单测试区域 -->
        <div class="section">
          <h2>📝 表单输入测试</h2>
          <p>测试各种表单元素的输入操作</p>
          <div class="form-group">
            <label for="username">用户名:</label>
            <input
              type="text"
              id="username"
              class="form-control"
              placeholder="请输入用户名"
            />
          </div>
          <div class="form-group">
            <label for="email">邮箱:</label>
            <input
              type="email"
              id="email"
              class="form-control"
              placeholder="请输入邮箱地址"
            />
          </div>
          <div class="form-group">
            <label for="password">密码:</label>
            <input
              type="password"
              id="password"
              class="form-control"
              placeholder="请输入密码"
            />
          </div>
          <div class="form-group">
            <label for="message">留言:</label>
            <textarea
              id="message"
              class="form-control"
              rows="4"
              placeholder="请输入您的留言"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="country">国家:</label>
            <select id="country" class="form-control">
              <option value="">请选择国家</option>
              <option value="china">中国</option>
              <option value="usa">美国</option>
              <option value="japan">日本</option>
              <option value="korea">韩国</option>
            </select>
          </div>
          <button class="btn btn-primary" onclick="submitForm()">
            提交表单
          </button>
        </div>

        <!-- 循环操作测试区域 -->
        <div class="section">
          <h2>🔄 循环操作测试</h2>
          <p>测试循环点击和敏感词检测功能</p>

          <div id="tableBody">
            <table class="table">
              <thead>
                <tr>
                  <th>项目名称</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr class="core-table-tr">
                  <td>
                    <div class="align-start">
                      <div class="w-full">
                        <div class="text-neutral-text1">智能手机项目</div>
                      </div>
                    </div>
                  </td>
                  <td><span class="status status-active">进行中</span></td>
                  <td>
                    <div
                      class="draggable-handle"
                      draggable="true"
                      ondragstart="drag(event)"
                      ondragend="dragEnd(event)"
                      id="drag-item-1"
                      style="display: inline-block; margin-right: 10px; padding: 5px 10px; background: #9b59b6; color: white; border-radius: 15px; cursor: move; font-size: 12px;"
                    >
                      📱 拖拽
                    </div>
                    <button
                      class="pulse-button btn btn-primary"
                      onclick="handleItemClick(1)"
                    >
                      提报
                    </button>
                  </td>
                </tr>
                <tr class="core-table-tr">
                  <td>
                    <div class="align-start">
                      <div class="w-full">
                        <div class="text-neutral-text1">电脑硬件开发</div>
                      </div>
                    </div>
                  </td>
                  <td><span class="status status-pending">待审核</span></td>
                  <td>
                    <div
                      class="draggable-handle"
                      draggable="true"
                      ondragstart="drag(event)"
                      ondragend="dragEnd(event)"
                      id="drag-item-2"
                      style="display: inline-block; margin-right: 10px; padding: 5px 10px; background: #e74c3c; color: white; border-radius: 15px; cursor: move; font-size: 12px;"
                    >
                      💻 拖拽
                    </div>
                    <button
                      class="pulse-button btn btn-success"
                      onclick="handleItemClick(2)"
                    >
                      提报
                    </button>
                  </td>
                </tr>
                <tr class="core-table-tr">
                  <td>
                    <div class="align-start">
                      <div class="w-full">
                        <div class="text-neutral-text1">软件测试项目</div>
                      </div>
                    </div>
                  </td>
                  <td><span class="status status-active">进行中</span></td>
                  <td>
                    <div
                      class="draggable-handle"
                      draggable="true"
                      ondragstart="drag(event)"
                      ondragend="dragEnd(event)"
                      id="drag-item-3"
                      style="display: inline-block; margin-right: 10px; padding: 5px 10px; background: #f39c12; color: white; border-radius: 15px; cursor: move; font-size: 12px;"
                    >
                      🧪 拖拽
                    </div>
                    <button
                      class="pulse-button btn btn-warning"
                      onclick="handleItemClick(3)"
                    >
                      提报
                    </button>
                  </td>
                </tr>
                <tr class="core-table-tr">
                  <td>
                    <div class="align-start">
                      <div class="w-full">
                        <div class="text-neutral-text1">网站建设方案</div>
                      </div>
                    </div>
                  </td>
                  <td><span class="status status-inactive">已完成</span></td>
                  <td>
                    <div
                      class="draggable-handle"
                      draggable="true"
                      ondragstart="drag(event)"
                      ondragend="dragEnd(event)"
                      id="drag-item-4"
                      style="display: inline-block; margin-right: 10px; padding: 5px 10px; background: #27ae60; color: white; border-radius: 15px; cursor: move; font-size: 12px;"
                    >
                      🌐 拖拽
                    </div>
                    <button
                      class="pulse-button btn btn-danger"
                      onclick="handleItemClick(4)"
                    >
                      提报
                    </button>
                  </td>
                </tr>
                <tr class="core-table-tr">
                  <td>
                    <div class="align-start">
                      <div class="w-full">
                        <div class="text-neutral-text1">移动应用开发</div>
                      </div>
                    </div>
                  </td>
                  <td><span class="status status-active">进行中</span></td>
                  <td>
                    <div
                      class="draggable-handle"
                      draggable="true"
                      ondragstart="drag(event)"
                      ondragend="dragEnd(event)"
                      id="drag-item-5"
                      style="display: inline-block; margin-right: 10px; padding: 5px 10px; background: #3498db; color: white; border-radius: 15px; cursor: move; font-size: 12px;"
                    >
                      📱 拖拽
                    </div>
                    <button
                      class="pulse-button btn btn-primary"
                      onclick="handleItemClick(5)"
                    >
                      提报
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 等待操作测试区域 -->
        <div class="section">
          <h2>⏳ 等待操作测试</h2>
          <p>测试智能等待和延时功能</p>
          <button class="btn btn-primary" onclick="showLoadingDemo()">
            显示加载状态
          </button>
          <button class="btn btn-success" onclick="showDelayedContent()">
            延时显示内容
          </button>
          <button class="btn btn-warning" onclick="testDragOperation()" style="margin-left: 10px;">
            测试拖拽功能
          </button>
          <button class="btn btn-info" onclick="debugLoopConfig()" style="margin-left: 10px;">
            调试循环配置
          </button>
          <button class="btn btn-success" onclick="testFixedLoopDrag()" style="margin-left: 10px;">
            测试修复后的循环拖拽
          </button>
          <button class="btn btn-danger" onclick="testCompleteLoopContainer()" style="margin-left: 10px;">
            测试完整循环容器
          </button>
          <div
            id="loading-area"
            style="margin-top: 20px; min-height: 50px"
          ></div>
          <div id="delayed-content" style="margin-top: 20px; display: none">
            <div class="card">
              <h3>🎉 延时内容已显示</h3>
              <p>这个内容在3秒后显示，可以用来测试智能等待功能。</p>
            </div>
          </div>
        </div>

        <!-- 拖拽操作测试区域 -->
        <div class="section">
          <h2>🖱️ 拖拽操作测试</h2>
          <p>测试拖拽和移动功能</p>
          <div class="drag-area" id="drag-container">
            <p>拖拽区域 - 将下面的元素拖拽到这里</p>
          </div>
          <div style="margin-top: 20px">
            <div
              class="draggable-item"
              draggable="true"
              ondragstart="drag(event)"
              id="item1"
            >
              可拖拽项目 1
            </div>
            <div
              class="draggable-item"
              draggable="true"
              ondragstart="drag(event)"
              id="item2"
            >
              可拖拽项目 2
            </div>
            <div
              class="draggable-item"
              draggable="true"
              ondragstart="drag(event)"
              id="item3"
            >
              可拖拽项目 3
            </div>
          </div>
        </div>

        <!-- 列表操作测试区域 -->
        <div class="section">
          <h2>📋 列表操作测试</h2>
          <p>测试列表项的循环操作</p>
          <ul class="list-group">
            <li class="list-item" onclick="selectItem(this, 1)">
              <strong>任务 1:</strong> 完成项目文档编写
              <span class="status status-pending" style="float: right"
                >待处理</span
              >
            </li>
            <li class="list-item" onclick="selectItem(this, 2)">
              <strong>任务 2:</strong> 代码审查和优化
              <span class="status status-active" style="float: right"
                >进行中</span
              >
            </li>
            <li class="list-item" onclick="selectItem(this, 3)">
              <strong>任务 3:</strong> 测试用例设计
              <span class="status status-inactive" style="float: right"
                >已完成</span
              >
            </li>
            <li class="list-item" onclick="selectItem(this, 4)">
              <strong>任务 4:</strong> 部署到生产环境
              <span class="status status-pending" style="float: right"
                >待处理</span
              >
            </li>
            <li class="list-item" onclick="selectItem(this, 5)">
              <strong>任务 5:</strong> 用户培训和文档
              <span class="status status-active" style="float: right"
                >进行中</span
              >
            </li>
          </ul>
            <!-- 卡片网格测试区域 -->
            <div class="section">
                <h2>🃏 卡片网格测试</h2>
                <p>测试卡片布局的循环操作</p>
                <div class="card-grid">
                    <div class="card" onclick="selectCard(this, 1)">
                        <h3>产品 A</h3>
                        <p>这是产品A的详细描述，包含了丰富的功能特性。</p>
                        <button class="btn btn-primary">查看详情</button>
                    </div>
                    <div class="card" onclick="selectCard(this, 2)">
                        <h3>产品 B</h3>
                        <p>这是产品B的详细描述，具有创新的设计理念。</p>
                        <button class="btn btn-success">查看详情</button>
                    </div>
                    <div class="card" onclick="selectCard(this, 3)">
                        <h3>产品 C</h3>
                        <p>这是产品C的详细描述，提供优质的用户体验。</p>
                        <button class="btn btn-warning">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="section">
                <h2>📖 在任何页面使用自动化功能</h2>
                <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="margin-top: 0; color: #1976d2;">🚀 快速开始指南</h3>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>打开插件</strong>：点击浏览器工具栏中的插件图标</li>
                        <li><strong>诊断支持</strong>：点击"诊断"按钮检查当前页面的自动化支持状态</li>
                        <li><strong>修复问题</strong>：如果诊断发现问题，点击"是"进行自动修复</li>
                        <li><strong>创建工作流</strong>：使用设计器创建自动化工作流</li>
                        <li><strong>执行工作流</strong>：选择工作流并点击执行</li>
                    </ol>

                    <h3 style="color: #1976d2;">🔧 故障排除</h3>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>功能不工作</strong>：先点击"诊断"按钮检查问题</li>
                        <li><strong>拖拽没效果</strong>：确保使用了增强拖拽功能</li>
                        <li><strong>找不到元素</strong>：检查选择器是否正确</li>
                        <li><strong>页面刷新后失效</strong>：重新打开插件或刷新页面</li>
                    </ul>

                    <h3 style="color: #1976d2;">💡 支持的操作类型</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;">
                        <div style="background: white; padding: 10px; border-radius: 4px;">
                            <strong>🖱️ 点击操作</strong><br>
                            <small>支持各种元素的点击</small>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 4px;">
                            <strong>📝 输入操作</strong><br>
                            <small>文本输入和表单填写</small>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 4px;">
                            <strong>🎯 拖拽操作</strong><br>
                            <small>HTML5拖拽和位置移动</small>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 4px;">
                            <strong>🔄 循环操作</strong><br>
                            <small>批量处理和容器循环</small>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 4px;">
                            <strong>⏳ 等待操作</strong><br>
                            <small>智能等待和条件判断</small>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 4px;">
                            <strong>🔍 敏感词检测</strong><br>
                            <small>自动跳过敏感内容</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态反馈区域 -->
            <div class="section">
                <h2>📊 操作状态反馈</h2>
                <p>显示各种操作的执行结果</p>
                <div id="status-display" style="background: #f8f9fa; padding: 20px; border-radius: 10px; min-height: 100px;">
                    <p><strong>状态显示区域</strong></p>
                    <p>点击各种按钮和元素，这里会显示相应的反馈信息。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 通用状态显示函数
        function updateStatus(message, type = 'info') {
            const statusDisplay = document.getElementById('status-display');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#3498db',
                success: '#2ecc71',
                warning: '#f39c12',
                error: '#e74c3c'
            };

            statusDisplay.innerHTML += `
                <div style="margin: 10px 0; padding: 10px; background: white; border-left: 4px solid ${colors[type]}; border-radius: 5px;">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
            statusDisplay.scrollTop = statusDisplay.scrollHeight;
        }

        // 显示警告框
        function showAlert(message) {
            alert(message);
            updateStatus(`显示警告: ${message}`, 'info');
        }

        // 提交表单
        function submitForm() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const message = document.getElementById('message').value;
            const country = document.getElementById('country').value;

            if (!username || !email || !password) {
                updateStatus('表单提交失败: 请填写必填字段', 'error');
                return;
            }

            updateStatus(`表单提交成功: 用户名=${username}, 邮箱=${email}, 国家=${country}`, 'success');
        }

        // 处理表格项目点击
        function handleItemClick(id) {
            const row = event.target.closest('tr');
            const projectName = row.querySelector('.text-neutral-text1').textContent;
            updateStatus(`点击了项目: ${projectName} (ID: ${id})`, 'info');

            // 模拟页面跳转或弹窗
            setTimeout(() => {
                updateStatus(`项目 ${projectName} 的详情页面已加载`, 'success');
            }, 1000);
        }

        // 处理拖拽元素点击（可选功能）
        function handleDragClick(event, id) {
            event.stopPropagation(); // 阻止事件冒泡
            const dragElement = event.target;
            const dragText = dragElement.textContent || dragElement.innerText || '未知元素';
            updateStatus(`点击了拖拽元素: ${dragText} (ID: ${id})`, 'info');

            // 添加点击动画效果
            dragElement.style.transform = 'scale(0.9)';
            setTimeout(() => {
                dragElement.style.transform = '';
            }, 150);
        }

        // 测试拖拽功能
        async function testDragOperation() {
            updateStatus('开始测试拖拽功能...', 'info');

            try {
                // 找到第一个拖拽元素
                const dragElement = document.querySelector('.draggable-handle');
                if (!dragElement) {
                    updateStatus('未找到拖拽元素', 'error');
                    return;
                }

                updateStatus(`找到拖拽元素: ${dragElement.id}`, 'info');

                // 模拟拖拽操作
                const rect = dragElement.getBoundingClientRect();
                const startX = rect.left + rect.width / 2;
                const startY = rect.top + rect.height / 2;
                const endX = startX + 200; // 向右移动200px
                const endY = startY + 50;  // 向下移动50px

                // 添加视觉高亮
                const originalStyle = dragElement.style.cssText;
                dragElement.style.border = '3px solid #ff6b6b';
                dragElement.style.boxShadow = '0 0 10px rgba(255, 107, 107, 0.5)';
                dragElement.style.transform = 'scale(1.1)';

                updateStatus(`开始拖拽: 从(${Math.round(startX)}, ${Math.round(startY)}) 到 (${Math.round(endX)}, ${Math.round(endY)})`, 'info');

                // 执行拖拽
                await performTestDrag(dragElement, startX, startY, endX, endY);

                // 恢复样式
                dragElement.style.cssText = originalStyle;

                updateStatus('拖拽测试完成！', 'success');
            } catch (error) {
                updateStatus(`拖拽测试失败: ${error.message}`, 'error');
            }
        }

        // 执行测试拖拽
        async function performTestDrag(element, startX, startY, endX, endY) {
            // 1. mousedown
            const mouseDownEvent = new MouseEvent('mousedown', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: startX,
                clientY: startY,
                button: 0,
                buttons: 1
            });
            element.dispatchEvent(mouseDownEvent);
            console.log('🖱️ 测试拖拽: mousedown 事件已触发');

            await new Promise(resolve => setTimeout(resolve, 200));

            // 2. mousemove (分步移动)
            const steps = 10;
            for (let i = 1; i <= steps; i++) {
                const progress = i / steps;
                const currentX = startX + (endX - startX) * progress;
                const currentY = startY + (endY - startY) * progress;

                const mouseMoveEvent = new MouseEvent('mousemove', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: currentX,
                    clientY: currentY,
                    button: 0,
                    buttons: 1
                });

                document.dispatchEvent(mouseMoveEvent);
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            console.log('🖱️ 测试拖拽: mousemove 事件序列已完成');

            // 3. mouseup
            const mouseUpEvent = new MouseEvent('mouseup', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: endX,
                clientY: endY,
                button: 0,
                buttons: 0
            });
            element.dispatchEvent(mouseUpEvent);
            console.log('🖱️ 测试拖拽: mouseup 事件已触发');

            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 调试循环配置
        function debugLoopConfig() {
            updateStatus('开始调试循环配置...', 'info');

            // 检查循环容器元素
            const loopElements = document.querySelectorAll('.core-table-tr');
            updateStatus(`找到 ${loopElements.length} 个循环容器元素`, 'info');

            loopElements.forEach((element, index) => {
                console.log(`循环容器 ${index + 1}:`, element);
                console.log(`  - 标签名: ${element.tagName}`);
                console.log(`  - 类名: ${element.className}`);
                console.log(`  - ID: ${element.id || '无'}`);

                // 检查容器内的拖拽元素
                const dragElement = element.querySelector('.draggable-handle');
                if (dragElement) {
                    console.log(`  - 找到拖拽元素: ${dragElement.id}`);
                    console.log(`  - 拖拽元素文本: ${dragElement.textContent.trim()}`);
                } else {
                    console.log(`  - 未找到拖拽元素`);
                }

                // 检查容器内的按钮
                const button = element.querySelector('.pulse-button');
                if (button) {
                    console.log(`  - 找到按钮: ${button.textContent.trim()}`);
                } else {
                    console.log(`  - 未找到按钮`);
                }
            });

            // 输出测试配置
            console.log('测试配置:', window.testLoopContainerConfig);
            console.log('简化测试配置:', window.simpleDragTest);

            updateStatus('循环配置调试完成，请查看控制台输出', 'success');
        }

        // 测试修复后的循环容器拖拽
        async function testFixedLoopDrag() {
            updateStatus('开始测试修复后的循环容器拖拽...', 'info');

            try {
                // 模拟循环容器操作
                const tableRows = document.querySelectorAll('.core-table-tr');
                if (tableRows.length === 0) {
                    updateStatus('未找到表格行元素', 'error');
                    return;
                }

                updateStatus(`找到 ${tableRows.length} 个表格行，开始测试前2行`, 'info');

                for (let i = 0; i < Math.min(2, tableRows.length); i++) {
                    const row = tableRows[i];
                    updateStatus(`📦 处理第 ${i + 1} 行容器`, 'info');

                    // 在当前行内查找拖拽元素
                    const dragElement = row.querySelector('.draggable-handle');
                    if (!dragElement) {
                        updateStatus(`第 ${i + 1} 行未找到拖拽元素`, 'warning');
                        continue;
                    }

                    updateStatus(`🖱️ 开始拖拽: ${dragElement.id} (100px, 50px)`, 'info');

                    // 执行拖拽
                    const rect = dragElement.getBoundingClientRect();
                    const startX = rect.left + rect.width / 2;
                    const startY = rect.top + rect.height / 2;
                    const endX = startX + 100;
                    const endY = startY + 50;

                    // 高亮显示
                    const originalStyle = dragElement.style.cssText;
                    dragElement.style.border = '3px solid #ff6b6b';
                    dragElement.style.boxShadow = '0 0 10px rgba(255, 107, 107, 0.5)';

                    await performTestDrag(dragElement, startX, startY, endX, endY);

                    // 恢复样式
                    dragElement.style.cssText = originalStyle;

                    updateStatus(`✅ 拖拽完成: ${dragElement.id}`, 'success');

                    // 等待一下再处理下一个
                    await new Promise(resolve => setTimeout(resolve, 1500));
                }

                updateStatus('✅ 循环容器拖拽测试完成！', 'success');
            } catch (error) {
                updateStatus(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试完整的循环容器操作（拖拽+点击）
        async function testCompleteLoopContainer() {
            updateStatus('开始测试完整循环容器操作...', 'info');

            try {
                // 模拟完整的循环容器操作
                const tableRows = document.querySelectorAll('.core-table-tr');
                if (tableRows.length === 0) {
                    updateStatus('未找到表格行元素', 'error');
                    return;
                }

                updateStatus(`找到 ${tableRows.length} 个表格行，开始测试前2行`, 'info');

                for (let i = 0; i < Math.min(2, tableRows.length); i++) {
                    const row = tableRows[i];
                    updateStatus(`📦 处理第 ${i + 1} 行容器`, 'info');

                    // 1. 拖拽操作
                    const dragElement = row.querySelector('.draggable-handle');
                    if (dragElement) {
                        updateStatus(`🖱️ 开始拖拽: ${dragElement.id} (100px, 50px)`, 'info');

                        // 执行拖拽
                        const rect = dragElement.getBoundingClientRect();
                        const startX = rect.left + rect.width / 2;
                        const startY = rect.top + rect.height / 2;
                        const endX = startX + 100;
                        const endY = startY + 50;

                        // 高亮显示
                        const originalStyle = dragElement.style.cssText;
                        dragElement.style.border = '3px solid #ff6b6b';
                        dragElement.style.boxShadow = '0 0 10px rgba(255, 107, 107, 0.5)';

                        // 使用增强拖拽
                        if (dragElement.draggable) {
                            // HTML5拖拽元素 - 直接移动
                            dragElement.style.transition = 'transform 300ms ease-out';
                            dragElement.style.transform = `translate(100px, 50px)`;
                            await new Promise(resolve => setTimeout(resolve, 300));

                            // 触发拖拽事件
                            const dragStartEvent = new DragEvent('dragstart', {
                                bubbles: true,
                                cancelable: true,
                                clientX: startX,
                                clientY: startY
                            });
                            dragElement.dispatchEvent(dragStartEvent);

                            await new Promise(resolve => setTimeout(resolve, 100));

                            const dragEndEvent = new DragEvent('dragend', {
                                bubbles: true,
                                cancelable: true,
                                clientX: endX,
                                clientY: endY
                            });
                            dragElement.dispatchEvent(dragEndEvent);

                            await new Promise(resolve => setTimeout(resolve, 1000));

                            // 恢复位置
                            dragElement.style.transform = '';
                        } else {
                            await performTestDrag(dragElement, startX, startY, endX, endY);
                        }

                        // 恢复样式
                        dragElement.style.cssText = originalStyle;
                        updateStatus(`✅ 拖拽完成: ${dragElement.id}`, 'success');
                    } else {
                        updateStatus(`第 ${i + 1} 行未找到拖拽元素`, 'warning');
                    }

                    // 2. 点击操作
                    const button = row.querySelector('.pulse-button');
                    if (button) {
                        updateStatus(`👆 点击: ${button.textContent.trim()}`, 'info');

                        // 高亮按钮
                        const originalButtonStyle = button.style.cssText;
                        button.style.border = '3px solid #3498db';
                        button.style.boxShadow = '0 0 10px rgba(52, 152, 219, 0.5)';

                        // 执行点击
                        button.click();

                        await new Promise(resolve => setTimeout(resolve, 500));

                        // 恢复样式
                        button.style.cssText = originalButtonStyle;
                        updateStatus(`✅ 点击完成: ${button.textContent.trim()}`, 'success');
                    } else {
                        updateStatus(`第 ${i + 1} 行未找到按钮元素`, 'warning');
                    }

                    // 等待一下再处理下一个
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }

                updateStatus('✅ 完整循环容器测试完成！', 'success');
            } catch (error) {
                updateStatus(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 显示加载演示
        function showLoadingDemo() {
            const loadingArea = document.getElementById('loading-area');
            loadingArea.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                    <p style="margin-top: 10px;">加载中...</p>
                </div>
            `;

            updateStatus('开始显示加载状态', 'info');

            setTimeout(() => {
                loadingArea.innerHTML = `
                    <div class="card">
                        <h3>✅ 加载完成</h3>
                        <p>内容已成功加载，可以测试智能等待功能。</p>
                    </div>
                `;
                updateStatus('加载状态完成', 'success');
            }, 3000);
        }

        // 显示延时内容
        function showDelayedContent() {
            updateStatus('开始延时显示内容 (3秒后显示)', 'info');

            setTimeout(() => {
                document.getElementById('delayed-content').style.display = 'block';
                updateStatus('延时内容已显示', 'success');
            }, 3000);
        }

        // 选择列表项
        function selectItem(element, id) {
            // 清除其他选中状态
            document.querySelectorAll('.list-item').forEach(item => {
                item.style.background = '';
            });

            // 设置当前选中状态
            element.style.background = '#e8f4f8';

            const taskName = element.querySelector('strong').textContent;
            updateStatus(`选择了任务: ${taskName}`, 'info');
        }

        // 选择卡片
        function selectCard(element, id) {
            // 清除其他选中状态
            document.querySelectorAll('.card').forEach(card => {
                card.style.border = '1px solid #ecf0f1';
            });

            // 设置当前选中状态
            element.style.border = '3px solid #3498db';

            const productName = element.querySelector('h3').textContent;
            updateStatus(`选择了产品: ${productName}`, 'info');
        }

        // 拖拽功能
        function drag(event) {
            console.log('拖拽开始:', event.target);
            event.dataTransfer.setData("text", event.target.id);
            event.dataTransfer.effectAllowed = "copy";

            const dragText = event.target.textContent || event.target.innerText || '未知元素';

            // 添加拖拽中的视觉效果
            event.target.classList.add('dragging');

            updateStatus(`开始拖拽: ${dragText}`, 'info');
        }

        // 拖拽结束事件
        function dragEnd(event) {
            console.log('拖拽结束:', event.target);
            event.target.classList.remove('dragging');
        }

        // 设置拖拽区域
        document.addEventListener('DOMContentLoaded', function() {
            const dragContainer = document.getElementById('drag-container');

            dragContainer.addEventListener('dragover', function(event) {
                event.preventDefault();
                event.dataTransfer.dropEffect = "copy";
                console.log('拖拽悬停在目标区域');
                dragContainer.style.background = '#d5e8d4';
                dragContainer.style.borderColor = '#2ecc71';
            });

            dragContainer.addEventListener('dragleave', function(event) {
                dragContainer.style.background = '#ecf0f1';
                dragContainer.style.borderColor = '#bdc3c7';
            });

            dragContainer.addEventListener('drop', function(event) {
                event.preventDefault();
                console.log('拖拽放置事件触发');
                const data = event.dataTransfer.getData("text");
                console.log('拖拽数据:', data);
                const draggedElement = document.getElementById(data);
                console.log('找到的元素:', draggedElement);

                if (draggedElement) {
                    // 检查是否是表格中的拖拽元素
                    if (draggedElement.classList.contains('draggable-handle')) {
                        // 为表格拖拽元素创建一个副本
                        const clone = draggedElement.cloneNode(true);
                        clone.id = draggedElement.id + '-clone-' + Date.now();
                        clone.style.margin = '5px';
                        clone.draggable = false; // 副本不可再拖拽
                        clone.style.cursor = 'default';
                        clone.onclick = null;
                        dragContainer.appendChild(clone);

                        const dragText = draggedElement.textContent || draggedElement.innerText || '未知元素';
                        updateStatus(`拖拽完成: ${dragText} 已复制到拖拽区域`, 'success');
                    } else {
                        // 原有的拖拽逻辑
                        dragContainer.appendChild(draggedElement);
                        const dragText = draggedElement.textContent || draggedElement.innerText || '未知元素';
                        updateStatus(`拖拽完成: ${dragText} 已移动到拖拽区域`, 'success');
                    }

                    dragContainer.style.background = '#ecf0f1';
                    dragContainer.style.borderColor = '#bdc3c7';
                }
            });

            // 添加旋转动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);

            // 初始化状态
            updateStatus('页面加载完成，可以开始测试各种功能', 'success');

            // 测试拖拽元素是否正确设置
            const dragElements = document.querySelectorAll('.draggable-handle');
            console.log('找到的拖拽元素数量:', dragElements.length);
            dragElements.forEach((element, index) => {
                console.log(`拖拽元素 ${index + 1}:`, element.id, element.draggable);
            });

            // 添加循环容器测试配置示例
            window.testLoopContainerConfig = {
                type: "loop",
                name: "循环容器测试",
                loopType: "container",
                locator: { strategy: "css", value: ".core-table-tr" },  // 正确的循环容器选择器
                loopSelector: ".core-table-tr",  // 循环选择器
                operationType: "none", // 不点击容器本身
                maxIterations: 5,
                startIndex: 0,
                endIndex: -1,
                subOperations: [
                    {
                        type: "drag",
                        name: "拖拽操作",
                        locator: { strategy: "css", value: ".draggable-handle" },
                        horizontalDistance: 200,  // 增加拖拽距离使其更明显
                        verticalDistance: 50,     // 添加垂直移动
                        dragSpeed: 200,           // 减慢拖拽速度使其更明显
                        waitAfterDrag: 1000
                    },
                    {
                        type: "click",
                        name: "点击提报按钮",
                        locator: { strategy: "css", value: ".pulse-button" },
                        waitAfterClick: 1000
                    }
                ]
            };

            // 添加简化的拖拽测试配置
            window.simpleDragTest = {
                type: "loop",
                name: "简单拖拽测试",
                loopType: "container",
                locator: { strategy: "css", value: ".core-table-tr" },
                loopSelector: ".core-table-tr",
                operationType: "none",
                maxIterations: 2,  // 只测试前2行
                subOperations: [
                    {
                        type: "drag",
                        name: "拖拽测试",
                        locator: { strategy: "css", value: ".draggable-handle" },
                        horizontalDistance: 150,
                        verticalDistance: 30,
                        dragSpeed: 300,
                        waitAfterDrag: 2000
                    }
                ]
            };
            console.log('循环容器测试配置已加载:', window.testLoopContainerConfig);

            // 重要提示：确保循环选择器配置正确
            console.log('⚠️ 重要提示：');
            console.log('1. 循环选择器应该是 .core-table-tr (表格行)');
            console.log('2. 不应该是 .pulse-button (按钮)');
            console.log('3. 当前配置的循环选择器:', window.testLoopContainerConfig.locator.value);
        });
    </script>
</body>
</html>
