/**
 * 页面浮层组件
 * 提供快速执行插件工作流的浮动界面
 */

class FloatingPanel {
  constructor() {
    this.isVisible = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.workflows = [];
    this.selectedWorkflow = null;
    this.executionState = {
      isRunning: false,
      isPaused: false,
      progress: 0,
      currentStep: '',
      totalSteps: 0
    };
    
    this.init();
  }

  /**
   * 初始化浮层
   */
  async init() {
    try {
      // 创建浮层容器
      this.createFloatingPanel();
      
      // 加载工作流数据
      await this.loadWorkflows();
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 监听插件数据变化
      this.setupDataSync();
      
      console.log('✅ 浮层组件初始化完成');
    } catch (error) {
      console.error('❌ 浮层初始化失败:', error);
    }
  }

  /**
   * 创建浮层DOM结构
   */
  createFloatingPanel() {
    // 创建主容器
    this.container = document.createElement('div');
    this.container.id = 'automation-floating-panel';
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 320px;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      display: none;
      user-select: none;
    `;

    // 创建标题栏
    const header = document.createElement('div');
    header.className = 'floating-panel-header';
    header.style.cssText = `
      background: #f5f5f5;
      padding: 12px 16px;
      border-bottom: 1px solid #e0e0e0;
      border-radius: 8px 8px 0 0;
      cursor: move;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    
    const title = document.createElement('span');
    title.textContent = '自动化执行面板';
    title.style.fontWeight = '600';
    
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.cssText = `
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
    closeBtn.onclick = () => this.hide();
    
    header.appendChild(title);
    header.appendChild(closeBtn);

    // 创建内容区域
    const content = document.createElement('div');
    content.className = 'floating-panel-content';
    content.style.cssText = `
      padding: 16px;
    `;

    // 工作流选择区域
    const workflowSection = document.createElement('div');
    workflowSection.style.marginBottom = '16px';
    
    const workflowLabel = document.createElement('label');
    workflowLabel.textContent = '选择工作流：';
    workflowLabel.style.cssText = `
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    `;
    
    this.workflowSelect = document.createElement('select');
    this.workflowSelect.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d0d0d0;
      border-radius: 4px;
      background: white;
    `;
    
    workflowSection.appendChild(workflowLabel);
    workflowSection.appendChild(this.workflowSelect);

    // 执行控制区域
    const controlSection = document.createElement('div');
    controlSection.style.marginBottom = '16px';
    
    this.executeBtn = document.createElement('button');
    this.executeBtn.textContent = '开始执行';
    this.executeBtn.style.cssText = `
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 8px;
      font-size: 14px;
    `;
    
    this.pauseBtn = document.createElement('button');
    this.pauseBtn.textContent = '暂停';
    this.pauseBtn.style.cssText = `
      background: #ffc107;
      color: #212529;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 8px;
      font-size: 14px;
      display: none;
    `;
    
    this.stopBtn = document.createElement('button');
    this.stopBtn.textContent = '停止';
    this.stopBtn.style.cssText = `
      background: #dc3545;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: none;
    `;
    
    controlSection.appendChild(this.executeBtn);
    controlSection.appendChild(this.pauseBtn);
    controlSection.appendChild(this.stopBtn);

    // 状态显示区域
    this.statusSection = document.createElement('div');
    this.statusSection.style.cssText = `
      padding: 12px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #28a745;
    `;
    
    this.statusText = document.createElement('div');
    this.statusText.textContent = '等待执行...';
    this.statusText.style.cssText = `
      margin-bottom: 8px;
      font-weight: 500;
    `;
    
    this.progressBar = document.createElement('div');
    this.progressBar.style.cssText = `
      width: 100%;
      height: 6px;
      background: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
      display: none;
    `;
    
    this.progressFill = document.createElement('div');
    this.progressFill.style.cssText = `
      height: 100%;
      background: #007bff;
      width: 0%;
      transition: width 0.3s ease;
    `;
    
    this.progressBar.appendChild(this.progressFill);
    this.statusSection.appendChild(this.statusText);
    this.statusSection.appendChild(this.progressBar);

    // 组装DOM
    content.appendChild(workflowSection);
    content.appendChild(controlSection);
    content.appendChild(this.statusSection);
    
    this.container.appendChild(header);
    this.container.appendChild(content);
    
    // 添加到页面
    document.body.appendChild(this.container);
    
    // 设置拖拽
    this.setupDragging(header);
  }

  /**
   * 设置拖拽功能
   */
  setupDragging(dragHandle) {
    dragHandle.addEventListener('mousedown', (e) => {
      this.isDragging = true;
      const rect = this.container.getBoundingClientRect();
      this.dragOffset.x = e.clientX - rect.left;
      this.dragOffset.y = e.clientY - rect.top;
      
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.handleDragEnd);
    });
  }

  handleDrag = (e) => {
    if (!this.isDragging) return;
    
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;
    
    // 限制在视窗内
    const maxX = window.innerWidth - this.container.offsetWidth;
    const maxY = window.innerHeight - this.container.offsetHeight;
    
    this.container.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
    this.container.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
    this.container.style.right = 'auto';
  };

  handleDragEnd = () => {
    this.isDragging = false;
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.handleDragEnd);
  };

  /**
   * 加载工作流数据
   */
  async loadWorkflows() {
    console.log(chrome.storage, '？？？？？？？？');
    
    try {
      console.log('🔍 开始从插件获取工作流数据...');

      // 通过chrome.storage API获取插件存储的数据
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        try {
          const result = await new Promise((resolve, reject) => {
            chrome.storage.local.get(['automationWorkflows'], (result) => {
              if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                resolve(result);
              }
            });
          });

          if (result.automationWorkflows && Array.isArray(result.automationWorkflows)) {
            console.log(`✅ 从chrome.storage获取了 ${result.automationWorkflows.length} 个工作流`);
            this.workflows = result.automationWorkflows;
            this.updateWorkflowSelect();
            this.updateStatus(`已加载 ${result.automationWorkflows.length} 个工作流`, 'success');
            return;
          }
        } catch (storageError) {
          console.warn('从chrome.storage获取数据失败:', storageError);
        }
      }

      // 如果chrome.storage不可用，通过content script消息获取
      console.log('🔄 尝试通过content script获取工作流数据...');
      try {
        const response = await this.sendMessageToExtension({
          action: 'getWorkflows'
        });

        if (response && response.success && response.workflows && Array.isArray(response.workflows)) {
          console.log(`✅ 通过消息获取了 ${response.workflows.length} 个工作流`);
          this.workflows = response.workflows;
          this.updateWorkflowSelect();
          this.updateStatus(`已加载 ${response.workflows.length} 个工作流`, 'success');
        } else {
          throw new Error('消息响应无效或无工作流数据');
        }
      } catch (msgError) {
        console.warn('通过消息获取工作流失败:', msgError);
        this.workflows = [];
        this.updateWorkflowSelect();
        this.updateStatus('未找到工作流配置，请先在插件中创建工作流', 'warning');
      }
    } catch (error) {
      console.error('加载工作流失败:', error);
      this.workflows = [];
      this.updateWorkflowSelect();
      this.updateStatus('加载工作流失败', 'error');
    }
  }

  /**
   * 更新工作流选择框
   */
  updateWorkflowSelect() {
    // 清空现有选项
    this.workflowSelect.innerHTML = '<option value="">请选择工作流...</option>';
    
    // 添加工作流选项
    this.workflows.forEach((workflow, index) => {
      const option = document.createElement('option');
      option.value = index;
      option.textContent = workflow.name || `工作流 ${index + 1}`;
      this.workflowSelect.appendChild(option);
    });
  }

  /**
   * 发送消息到插件
   */
  async sendMessageToExtension(message) {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      } else {
        reject(new Error('Chrome extension API not available'));
      }
    });
  }

  /**
   * 显示浮层
   */
  show() {
    this.container.style.display = 'block';
    this.isVisible = true;
    this.loadWorkflows(); // 重新加载最新数据
  }

  /**
   * 隐藏浮层
   */
  hide() {
    this.container.style.display = 'none';
    this.isVisible = false;
  }

  /**
   * 切换显示状态
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 工作流选择变化
    this.workflowSelect.addEventListener('change', (e) => {
      const index = parseInt(e.target.value);
      if (!isNaN(index) && this.workflows[index]) {
        this.selectedWorkflow = this.workflows[index];
        this.updateStatus(`已选择: ${this.selectedWorkflow.name}`, 'info');
      } else {
        this.selectedWorkflow = null;
        this.updateStatus('请选择工作流', 'info');
      }
    });

    // 执行按钮
    this.executeBtn.addEventListener('click', () => {
      this.executeWorkflow();
    });

    // 暂停/继续按钮
    this.pauseBtn.addEventListener('click', () => {
      this.togglePause();
    });

    // 停止按钮
    this.stopBtn.addEventListener('click', () => {
      this.stopExecution();
    });
  }

  /**
   * 设置数据同步监听
   */
  setupDataSync() {
    // 监听chrome.storage变化
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'local' && changes.automationWorkflows) {
          console.log('检测到插件工作流数据变化，重新加载');
          this.loadWorkflows();
        }
      });
    }

    // 监听localStorage变化（兼容性）
    window.addEventListener('storage', (e) => {
      if (e.key === 'automationWorkflows') {
        console.log('检测到localStorage工作流数据变化，重新加载');
        this.loadWorkflows();
      }
    });

    // 监听插件执行状态变化
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message, _sender, _sendResponse) => {
        if (message.action === 'executionProgress') {
          this.updateExecutionProgress(message.data);
        } else if (message.action === 'executionCompleted') {
          this.handleExecutionCompleted(message.data);
        } else if (message.action === 'executionError') {
          this.handleExecutionError(message.data);
        }
      });
    }
  }

  /**
   * 执行工作流
   */
  async executeWorkflow() {
    if (!this.selectedWorkflow) {
      this.updateStatus('请先选择一个工作流', 'warning');
      return;
    }

    if (this.executionState.isRunning) {
      this.updateStatus('工作流正在执行中', 'warning');
      return;
    }

    try {
      this.executionState.isRunning = true;
      this.executionState.isPaused = false;
      this.updateExecutionUI();
      this.updateStatus('开始执行工作流...', 'running');

      // 通过消息传递调用插件的执行功能
      const response = await this.sendMessageToExtension({
        action: 'executeWorkflowFromFloatingPanel',
        workflow: this.selectedWorkflow
      });

      if (response && response.success) {
        this.updateStatus('工作流执行中...', 'running');
      } else {
        throw new Error(response?.error || '执行失败');
      }
    } catch (error) {
      console.error('执行工作流失败:', error);
      this.updateStatus(`执行失败: ${error.message}`, 'error');
      this.resetExecutionState();
    }
  }

  /**
   * 暂停/继续执行
   */
  async togglePause() {
    if (!this.executionState.isRunning) return;

    try {
      const action = this.executionState.isPaused ? 'resumeExecution' : 'pauseExecution';
      const response = await this.sendMessageToExtension({ action });

      if (response && response.success) {
        this.executionState.isPaused = !this.executionState.isPaused;
        this.updateExecutionUI();
        this.updateStatus(
          this.executionState.isPaused ? '执行已暂停' : '继续执行中...',
          this.executionState.isPaused ? 'paused' : 'running'
        );
      }
    } catch (error) {
      console.error('暂停/继续失败:', error);
      this.updateStatus(`操作失败: ${error.message}`, 'error');
    }
  }

  /**
   * 停止执行
   */
  async stopExecution() {
    if (!this.executionState.isRunning) return;

    try {
      const response = await this.sendMessageToExtension({
        action: 'stopExecution'
      });

      if (response && response.success) {
        this.resetExecutionState();
        this.updateStatus('执行已停止', 'info');
      }
    } catch (error) {
      console.error('停止执行失败:', error);
      this.updateStatus(`停止失败: ${error.message}`, 'error');
      this.resetExecutionState();
    }
  }

  /**
   * 更新执行进度
   */
  updateExecutionProgress(data) {
    if (data.completedSteps !== undefined && data.totalSteps) {
      this.executionState.progress = (data.completedSteps / data.totalSteps) * 100;
      this.progressFill.style.width = this.executionState.progress + '%';
    }

    if (data.currentOperation) {
      this.executionState.currentStep = data.currentOperation;
      this.updateStatus(data.currentOperation, 'running');
    }
  }

  /**
   * 处理执行完成
   */
  handleExecutionCompleted(data) {
    this.resetExecutionState();
    this.updateStatus('工作流执行完成', 'success');
    this.progressFill.style.width = '100%';

    setTimeout(() => {
      this.progressBar.style.display = 'none';
      this.progressFill.style.width = '0%';
    }, 2000);
  }

  /**
   * 处理执行错误
   */
  handleExecutionError(data) {
    this.resetExecutionState();
    this.updateStatus(`执行失败: ${data.error || '未知错误'}`, 'error');
  }

  /**
   * 重置执行状态
   */
  resetExecutionState() {
    this.executionState.isRunning = false;
    this.executionState.isPaused = false;
    this.executionState.progress = 0;
    this.executionState.currentStep = '';
    this.updateExecutionUI();
  }

  /**
   * 更新执行UI状态
   */
  updateExecutionUI() {
    if (this.executionState.isRunning) {
      this.executeBtn.style.display = 'none';
      this.pauseBtn.style.display = 'inline-block';
      this.stopBtn.style.display = 'inline-block';
      this.progressBar.style.display = 'block';

      this.pauseBtn.textContent = this.executionState.isPaused ? '继续' : '暂停';
      this.pauseBtn.style.background = this.executionState.isPaused ? '#28a745' : '#ffc107';
    } else {
      this.executeBtn.style.display = 'inline-block';
      this.pauseBtn.style.display = 'none';
      this.stopBtn.style.display = 'none';
      this.progressBar.style.display = 'none';
    }
  }

  /**
   * 更新状态显示
   */
  updateStatus(message, type = 'info') {
    this.statusText.textContent = message;

    // 更新状态样式
    const colors = {
      info: '#17a2b8',
      success: '#28a745',
      warning: '#ffc107',
      error: '#dc3545',
      running: '#007bff',
      paused: '#6c757d'
    };

    this.statusSection.style.borderLeftColor = colors[type] || colors.info;
  }
}

// 导出到全局作用域
window.FloatingPanel = FloatingPanel;
