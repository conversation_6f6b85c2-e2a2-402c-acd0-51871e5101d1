/**
 * 浮层自动化系统主入口文件
 * 负责加载和初始化浮层组件
 */

(function() {
  'use strict';

  // 防止重复加载
  if (window.floatingAutomationLoaded) {
    console.log('🔄 浮层自动化系统已加载，跳过重复初始化');
    return;
  }

  console.log('🚀 开始加载浮层自动化系统...');

  /**
   * 动态加载脚本
   * @param {string} src - 脚本路径
   * @returns {Promise} 加载Promise
   */
  function loadScript(src) {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载过
      const existingScript = document.querySelector(`script[src*="${src}"]`);
      if (existingScript) {
        console.log(`✅ 脚本已存在: ${src}`);
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = chrome.runtime.getURL(src);
      script.onload = () => {
        console.log(`✅ 脚本加载成功: ${src}`);
        resolve();
      };
      script.onerror = (error) => {
        console.error(`❌ 脚本加载失败: ${src}`, error);
        reject(error);
      };
      document.documentElement.appendChild(script);
    });
  }

  /**
   * 检查Chrome扩展API是否可用
   */
  function checkExtensionAPI() {
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      throw new Error('Chrome扩展API不可用');
    }
    console.log('✅ Chrome扩展API可用');
  }

  /**
   * 初始化浮层系统
   */
  async function initializeFloatingSystem() {
    try {
      // 检查扩展API
      checkExtensionAPI();

      console.log('📦 开始加载浮层组件...');

      // 按顺序加载组件
      await loadScript('floating-panel.js');
      console.log('✅ 浮层面板组件加载完成');

      await loadScript('floating-trigger.js');
      console.log('✅ 浮层触发器组件加载完成');

      // 等待DOM完全加载
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // 验证组件是否正确加载
      if (typeof window.FloatingPanel === 'undefined') {
        throw new Error('FloatingPanel组件未正确加载');
      }

      if (typeof window.FloatingTrigger === 'undefined') {
        throw new Error('FloatingTrigger组件未正确加载');
      }

      // 等待触发器初始化完成
      let attempts = 0;
      const maxAttempts = 10;
      const checkTrigger = () => {
        attempts++;
        if (window.floatingTriggerInstance) {
          console.log('✅ 浮层触发器实例已创建');
        } else if (attempts < maxAttempts) {
          console.log(`⏳ 等待触发器初始化... (${attempts}/${maxAttempts})`);
          setTimeout(checkTrigger, 200);
        } else {
          console.warn('⚠️ 触发器初始化超时，但系统仍可正常使用');
        }
      };

      setTimeout(checkTrigger, 100);

      console.log('🎉 浮层自动化系统初始化完成！');
      
      // 标记已加载
      window.floatingAutomationLoaded = true;

      // 发送初始化完成消息
      try {
        chrome.runtime.sendMessage({
          action: 'floatingSystemInitialized',
          timestamp: Date.now()
        });
      } catch (error) {
        console.warn('发送初始化消息失败:', error);
      }

    } catch (error) {
      console.error('❌ 浮层自动化系统初始化失败:', error);
      
      // 发送错误消息
      try {
        chrome.runtime.sendMessage({
          action: 'floatingSystemError',
          error: error.message,
          timestamp: Date.now()
        });
      } catch (msgError) {
        console.warn('发送错误消息失败:', msgError);
      }
    }
  }

  /**
   * 检查页面是否适合加载浮层
   */
  function isPageSuitable() {
    // 排除特殊页面
    const excludePatterns = [
      /^chrome:/,
      /^chrome-extension:/,
      /^moz-extension:/,
      /^about:/,
      /^file:/
    ];

    const currentUrl = window.location.href;
    
    for (const pattern of excludePatterns) {
      if (pattern.test(currentUrl)) {
        console.log(`🚫 页面不适合加载浮层: ${currentUrl}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 延迟初始化
   */
  function delayedInitialization() {
    console.log('🔍 开始延迟初始化检查...');
    console.log('🔍 当前页面URL:', window.location.href);
    console.log('🔍 document.readyState:', document.readyState);

    // 检查页面是否适合
    if (!isPageSuitable()) {
      console.log('🚫 页面不适合加载浮层，跳过初始化');
      return;
    }

    console.log('✅ 页面适合加载浮层，开始初始化...');

    // 延迟一段时间确保页面稳定
    setTimeout(() => {
      console.log('⏰ 延迟时间到，开始初始化浮层系统');
      initializeFloatingSystem();
    }, 1000);
  }

  // 根据页面加载状态决定初始化时机
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', delayedInitialization);
  } else {
    delayedInitialization();
  }

  // 监听页面可见性变化，在页面重新可见时检查组件状态
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.floatingAutomationLoaded) {
      // 页面重新可见时，检查触发器是否还存在
      setTimeout(() => {
        if (!document.getElementById('automation-floating-trigger')) {
          console.log('🔄 检测到触发器丢失，重新初始化...');
          window.floatingAutomationLoaded = false;
          delayedInitialization();
        }
      }, 500);
    }
  });

  // 监听扩展消息
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'toggleFloatingPanel') {
        if (window.floatingTriggerInstance && window.floatingTriggerInstance.floatingPanel) {
          window.floatingTriggerInstance.floatingPanel.toggle();
          sendResponse({ success: true });
        } else {
          sendResponse({ success: false, error: '浮层面板未初始化' });
        }
        return true;
      }

      if (message.action === 'showFloatingPanel') {
        if (window.floatingTriggerInstance && window.floatingTriggerInstance.floatingPanel) {
          window.floatingTriggerInstance.floatingPanel.show();
          sendResponse({ success: true });
        } else {
          sendResponse({ success: false, error: '浮层面板未初始化' });
        }
        return true;
      }

      if (message.action === 'hideFloatingPanel') {
        if (window.floatingTriggerInstance && window.floatingTriggerInstance.floatingPanel) {
          window.floatingTriggerInstance.floatingPanel.hide();
          sendResponse({ success: true });
        } else {
          sendResponse({ success: false, error: '浮层面板未初始化' });
        }
        return true;
      }
    });
  }

  console.log('📋 浮层自动化系统脚本已加载');

})();
