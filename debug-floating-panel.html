<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮层面板调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h2>🔧 浮层面板调试工具</h2>
        <p>此页面用于调试浮层面板功能，无需依赖插件环境。</p>
        
        <div>
            <button class="debug-button" onclick="createTrigger()">创建触发器</button>
            <button class="debug-button" onclick="createPanel()">创建面板</button>
            <button class="debug-button" onclick="testWorkflows()">测试工作流数据</button>
            <button class="debug-button" onclick="clearLog()">清除日志</button>
        </div>
        
        <div id="status" class="status">等待操作...</div>
        
        <h3>调试日志</h3>
        <div id="debugLog" class="debug-log">页面加载完成，等待调试操作...\n</div>
    </div>

    <script>
        // 模拟Chrome扩展API
        window.chrome = {
            runtime: {
                getURL: function(path) {
                    return './' + path;
                },
                sendMessage: function(message, callback) {
                    log('发送消息: ' + JSON.stringify(message));
                    if (callback) {
                        setTimeout(() => {
                            if (message.action === 'getWorkflows') {
                                callback({
                                    success: true,
                                    workflows: [
                                        { name: '测试工作流 1', steps: [] },
                                        { name: '测试工作流 2', steps: [] }
                                    ]
                                });
                            } else {
                                callback({ success: true });
                            }
                        }, 100);
                    }
                },
                onMessage: {
                    addListener: function(callback) {
                        log('添加消息监听器');
                    }
                }
            }
        };

        // 模拟localStorage数据
        localStorage.setItem('automationWorkflows', JSON.stringify([
            {
                name: '测试工作流 1',
                steps: [
                    { type: 'click', name: '点击按钮', locator: { strategy: 'id', value: 'testBtn1' } }
                ]
            },
            {
                name: '测试工作流 2', 
                steps: [
                    { type: 'input', name: '输入文本', locator: { strategy: 'id', value: 'testInput' } }
                ]
            }
        ]));

        function log(message) {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(message, type = 'success') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = '';
            log('日志已清除');
        }

        function createTrigger() {
            try {
                log('开始创建浮层触发器...');
                
                // 检查是否已存在
                if (document.getElementById('automation-floating-trigger')) {
                    log('触发器已存在，先移除');
                    document.getElementById('automation-floating-trigger').remove();
                }

                // 创建触发器
                const trigger = document.createElement('div');
                trigger.id = 'automation-floating-trigger';
                trigger.style.cssText = `
                    position: fixed;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 50px;
                    height: 50px;
                    background: linear-gradient(135deg, #007bff, #0056b3);
                    border: none;
                    border-radius: 0 25px 25px 0;
                    cursor: pointer;
                    z-index: 999998;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 2px 0 8px rgba(0,0,0,0.2);
                    transition: all 0.3s ease;
                    user-select: none;
                `;

                trigger.innerHTML = `
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2"/>
                        <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2"/>
                        <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2"/>
                    </svg>
                `;

                trigger.onclick = () => {
                    log('触发器被点击');
                    createPanel();
                };

                document.body.appendChild(trigger);
                log('✅ 浮层触发器创建成功');
                setStatus('浮层触发器已创建', 'success');
            } catch (error) {
                log('❌ 创建触发器失败: ' + error.message);
                setStatus('创建触发器失败', 'error');
            }
        }

        function createPanel() {
            try {
                log('开始创建浮层面板...');
                
                // 检查是否已存在
                if (document.getElementById('automation-floating-panel')) {
                    const existing = document.getElementById('automation-floating-panel');
                    if (existing.style.display === 'none') {
                        existing.style.display = 'block';
                        log('显示已存在的浮层面板');
                        return;
                    } else {
                        existing.remove();
                        log('移除已存在的浮层面板');
                    }
                }

                // 创建面板容器
                const panel = document.createElement('div');
                panel.id = 'automation-floating-panel';
                panel.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 320px;
                    background: #ffffff;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 999999;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                `;

                // 创建面板内容
                panel.innerHTML = `
                    <div style="background: #f5f5f5; padding: 12px 16px; border-bottom: 1px solid #e0e0e0; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: 600;">自动化执行面板</span>
                        <button onclick="this.closest('#automation-floating-panel').style.display='none'" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                    </div>
                    <div style="padding: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">选择工作流：</label>
                            <select id="workflowSelect" style="width: 100%; padding: 8px 12px; border: 1px solid #d0d0d0; border-radius: 4px;">
                                <option value="">请选择工作流...</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <button id="executeBtn" onclick="executeWorkflow()" style="background: #007bff; color: white; border: none; padding: 10px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;">开始执行</button>
                            <button id="pauseBtn" onclick="pauseWorkflow()" style="background: #ffc107; color: #212529; border: none; padding: 10px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px; display: none;">暂停</button>
                            <button id="stopBtn" onclick="stopWorkflow()" style="background: #dc3545; color: white; border: none; padding: 10px 16px; border-radius: 4px; cursor: pointer; display: none;">停止</button>
                        </div>
                        <div id="statusSection" style="padding: 12px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #28a745;">
                            <div id="statusText">等待执行...</div>
                            <div id="progressBar" style="width: 100%; height: 6px; background: #e9ecef; border-radius: 3px; overflow: hidden; margin-top: 8px; display: none;">
                                <div id="progressFill" style="height: 100%; background: #007bff; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(panel);
                
                // 加载工作流数据
                loadWorkflows();
                
                log('✅ 浮层面板创建成功');
                setStatus('浮层面板已创建', 'success');
            } catch (error) {
                log('❌ 创建面板失败: ' + error.message);
                setStatus('创建面板失败', 'error');
            }
        }

        function loadWorkflows() {
            try {
                log('加载工作流数据...');
                const data = localStorage.getItem('automationWorkflows');
                if (data) {
                    const workflows = JSON.parse(data);
                    const select = document.getElementById('workflowSelect');
                    if (select) {
                        select.innerHTML = '<option value="">请选择工作流...</option>';
                        workflows.forEach((workflow, index) => {
                            const option = document.createElement('option');
                            option.value = index;
                            option.textContent = workflow.name;
                            select.appendChild(option);
                        });
                        log(`✅ 加载了 ${workflows.length} 个工作流`);
                    }
                }
            } catch (error) {
                log('❌ 加载工作流失败: ' + error.message);
            }
        }

        function executeWorkflow() {
            const select = document.getElementById('workflowSelect');
            if (select && select.value) {
                log(`开始执行工作流: ${select.options[select.selectedIndex].text}`);
                updateStatus('正在执行工作流...', 'running');
                
                // 模拟执行进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 20;
                    updateProgress(progress);
                    if (progress >= 100) {
                        clearInterval(interval);
                        updateStatus('工作流执行完成', 'success');
                        setTimeout(() => {
                            document.getElementById('progressBar').style.display = 'none';
                        }, 2000);
                    }
                }, 500);
            } else {
                log('❌ 请先选择一个工作流');
                updateStatus('请先选择一个工作流', 'warning');
            }
        }

        function pauseWorkflow() {
            log('暂停工作流执行');
            updateStatus('工作流已暂停', 'paused');
        }

        function stopWorkflow() {
            log('停止工作流执行');
            updateStatus('工作流已停止', 'info');
        }

        function updateStatus(message, type = 'info') {
            const statusText = document.getElementById('statusText');
            if (statusText) {
                statusText.textContent = message;
            }
            log(`状态更新: ${message} (${type})`);
        }

        function updateProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            if (progressBar && progressFill) {
                progressBar.style.display = 'block';
                progressFill.style.width = percent + '%';
            }
        }

        function testWorkflows() {
            log('测试工作流数据访问...');
            try {
                const data = localStorage.getItem('automationWorkflows');
                if (data) {
                    const workflows = JSON.parse(data);
                    log(`✅ 找到 ${workflows.length} 个工作流:`);
                    workflows.forEach((workflow, index) => {
                        log(`  ${index + 1}. ${workflow.name} (${workflow.steps.length} 个步骤)`);
                    });
                    setStatus(`找到 ${workflows.length} 个工作流`, 'success');
                } else {
                    log('❌ 未找到工作流数据');
                    setStatus('未找到工作流数据', 'warning');
                }
            } catch (error) {
                log('❌ 测试失败: ' + error.message);
                setStatus('测试失败', 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面加载完成');
            setStatus('调试页面已准备就绪', 'success');
        });
    </script>
</body>
</html>
