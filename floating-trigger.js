/**
 * 浮层触发器组件
 * 在页面左侧显示可拖拽的触发按钮
 */

class FloatingTrigger {
  constructor() {
    this.isVisible = true;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.floatingPanel = null;
    
    this.init();
  }

  /**
   * 初始化触发器
   */
  init() {
    try {
      console.log('🚀 开始初始化浮层触发器...');
      this.createTriggerButton();
      this.setupEventListeners();
      console.log('✅ 浮层触发器初始化完成');
    } catch (error) {
      console.error('❌ 浮层触发器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建触发按钮
   */
  createTriggerButton() {
    console.log('🔧 开始创建触发按钮...');

    // 检查是否已存在
    const existing = document.getElementById('automation-floating-trigger');
    if (existing) {
      console.log('⚠️ 触发器已存在，移除旧的');
      existing.remove();
    }

    this.trigger = document.createElement('div');
    this.trigger.id = 'automation-floating-trigger';
    this.trigger.style.cssText = `
      position: fixed !important;
      left: 0 !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: 50px !important;
      height: 50px !important;
      background: linear-gradient(135deg, #007bff, #0056b3) !important;
      border: none !important;
      border-radius: 0 25px 25px 0 !important;
      cursor: pointer !important;
      z-index: 999998 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-shadow: 2px 0 8px rgba(0,0,0,0.2) !important;
      transition: all 0.3s ease !important;
      user-select: none !important;
      pointer-events: auto !important;
    `;

    console.log('✅ 触发按钮元素已创建');

    // 创建图标
    const icon = document.createElement('div');
    icon.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
      </svg>
    `;
    icon.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    this.trigger.appendChild(icon);

    // 确保DOM准备好后再添加元素
    const addToDOM = () => {
      if (document.body) {
        document.body.appendChild(this.trigger);
        console.log('✅ 浮层触发器已添加到DOM');
      } else {
        console.log('⏳ DOM未准备好，延迟添加触发器');
        setTimeout(addToDOM, 100);
      }
    };

    addToDOM();

    // 添加悬停效果
    this.trigger.addEventListener('mouseenter', () => {
      this.trigger.style.transform = 'translateY(-50%) translateX(5px)';
      this.trigger.style.boxShadow = '3px 0 12px rgba(0,0,0,0.3)';
    });

    this.trigger.addEventListener('mouseleave', () => {
      if (!this.isDragging) {
        this.trigger.style.transform = 'translateY(-50%)';
        this.trigger.style.boxShadow = '2px 0 8px rgba(0,0,0,0.2)';
      }
    });
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 点击事件
    this.trigger.addEventListener('click', (e) => {
      if (!this.isDragging) {
        this.togglePanel();
      }
    });

    // 拖拽事件
    this.trigger.addEventListener('mousedown', (e) => {
      this.isDragging = true;
      const rect = this.trigger.getBoundingClientRect();
      this.dragOffset.x = e.clientX - rect.left;
      this.dragOffset.y = e.clientY - rect.top;
      
      // 防止点击事件触发
      setTimeout(() => {
        this.isDragging = false;
      }, 200);
      
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.handleDragEnd);
      
      e.preventDefault();
    });
  }

  /**
   * 处理拖拽
   */
  handleDrag = (e) => {
    if (!this.isDragging) return;
    
    const y = e.clientY - this.dragOffset.y;
    
    // 限制在视窗内，保持在左侧
    const maxY = window.innerHeight - this.trigger.offsetHeight;
    const constrainedY = Math.max(0, Math.min(y, maxY));
    
    this.trigger.style.top = constrainedY + 'px';
    this.trigger.style.transform = 'translateY(0)';
  };

  /**
   * 处理拖拽结束
   */
  handleDragEnd = () => {
    this.isDragging = false;
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.handleDragEnd);
    
    // 恢复居中变换，但保持新的top位置
    setTimeout(() => {
      if (!this.isDragging) {
        this.trigger.style.transform = 'translateY(-50%)';
      }
    }, 100);
  };

  /**
   * 切换面板显示
   */
  togglePanel() {
    if (!this.floatingPanel) {
      // 延迟加载浮层面板
      this.initFloatingPanel();
    }
    
    if (this.floatingPanel) {
      this.floatingPanel.toggle();
    }
  }

  /**
   * 初始化浮层面板
   */
  initFloatingPanel() {
    try {
      console.log('🔧 开始初始化浮层面板...');
      if (window.FloatingPanel) {
        if (!this.floatingPanel) {
          this.floatingPanel = new window.FloatingPanel();
          console.log('✅ 浮层面板已创建');
        } else {
          console.log('⚠️ 浮层面板已存在');
        }
      } else {
        console.error('❌ FloatingPanel 类未找到');
        // 尝试等待类加载
        setTimeout(() => {
          if (window.FloatingPanel && !this.floatingPanel) {
            this.floatingPanel = new window.FloatingPanel();
            console.log('✅ 延迟创建浮层面板成功');
          }
        }, 1000);
      }
    } catch (error) {
      console.error('❌ 创建浮层面板失败:', error);
    }
  }

  /**
   * 显示触发器
   */
  show() {
    this.trigger.style.display = 'flex';
    this.isVisible = true;
  }

  /**
   * 隐藏触发器
   */
  hide() {
    this.trigger.style.display = 'none';
    this.isVisible = false;
  }

  /**
   * 销毁触发器
   */
  destroy() {
    if (this.trigger && this.trigger.parentNode) {
      this.trigger.parentNode.removeChild(this.trigger);
    }
    if (this.floatingPanel) {
      this.floatingPanel.hide();
    }
  }
}

// 导出到全局作用域
window.FloatingTrigger = FloatingTrigger;

// 自动初始化逻辑
(function() {
  // 防止重复初始化
  if (window.floatingTriggerInstance) {
    console.log('🔄 浮层触发器已存在，跳过重复初始化');
    return;
  }

  function initializeTrigger() {
    try {
      console.log('🚀 开始初始化浮层触发器...');
      window.floatingTriggerInstance = new FloatingTrigger();
      console.log('✅ 自动化浮层触发器已启动');

      // 标记为已初始化
      window.floatingTriggerInitialized = true;
    } catch (error) {
      console.error('❌ 初始化浮层触发器失败:', error);

      // 如果初始化失败，稍后重试
      setTimeout(() => {
        if (!window.floatingTriggerInitialized) {
          console.log('🔄 浮层触发器初始化失败，重试...');
          initializeTrigger();
        }
      }, 2000);
    }
  }

  // 确保DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initializeTrigger, 500);
    });
  } else {
    // DOM已加载，延迟一点时间确保页面稳定
    setTimeout(initializeTrigger, 500);
  }

  // 额外的保险机制 - 在页面完全加载后再次检查
  window.addEventListener('load', () => {
    setTimeout(() => {
      if (!window.floatingTriggerInitialized) {
        console.log('🔄 页面加载完成后强制初始化浮层触发器...');
        initializeTrigger();
      }
    }, 1000);
  });

  // 导出手动初始化函数到全局作用域（用于调试）
  window.forceInitTrigger = function() {
    console.log('🔧 手动强制初始化浮层触发器...');
    window.floatingTriggerInitialized = false;
    window.floatingTriggerInstance = null;

    // 移除现有的触发器
    const existingTrigger = document.getElementById('automation-floating-trigger');
    if (existingTrigger) {
      existingTrigger.remove();
    }

    // 重新初始化
    initializeTrigger();
  };
})();
