/**
 * 浮层触发器组件
 * 在页面左侧显示可拖拽的触发按钮
 */

class FloatingTrigger {
  constructor() {
    this.isVisible = true;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.floatingPanel = null;
    
    this.init();
  }

  /**
   * 初始化触发器
   */
  init() {
    this.createTriggerButton();
    this.setupEventListeners();
    console.log('✅ 浮层触发器初始化完成');
  }

  /**
   * 创建触发按钮
   */
  createTriggerButton() {
    this.trigger = document.createElement('div');
    this.trigger.id = 'automation-floating-trigger';
    this.trigger.style.cssText = `
      position: fixed;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #007bff, #0056b3);
      border: none;
      border-radius: 0 25px 25px 0;
      cursor: pointer;
      z-index: 999998;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 2px 0 8px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
      user-select: none;
    `;

    // 创建图标
    const icon = document.createElement('div');
    icon.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
      </svg>
    `;
    icon.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    this.trigger.appendChild(icon);
    document.body.appendChild(this.trigger);

    // 添加悬停效果
    this.trigger.addEventListener('mouseenter', () => {
      this.trigger.style.transform = 'translateY(-50%) translateX(5px)';
      this.trigger.style.boxShadow = '3px 0 12px rgba(0,0,0,0.3)';
    });

    this.trigger.addEventListener('mouseleave', () => {
      if (!this.isDragging) {
        this.trigger.style.transform = 'translateY(-50%)';
        this.trigger.style.boxShadow = '2px 0 8px rgba(0,0,0,0.2)';
      }
    });
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 点击事件
    this.trigger.addEventListener('click', (e) => {
      if (!this.isDragging) {
        this.togglePanel();
      }
    });

    // 拖拽事件
    this.trigger.addEventListener('mousedown', (e) => {
      this.isDragging = true;
      const rect = this.trigger.getBoundingClientRect();
      this.dragOffset.x = e.clientX - rect.left;
      this.dragOffset.y = e.clientY - rect.top;
      
      // 防止点击事件触发
      setTimeout(() => {
        this.isDragging = false;
      }, 200);
      
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.handleDragEnd);
      
      e.preventDefault();
    });
  }

  /**
   * 处理拖拽
   */
  handleDrag = (e) => {
    if (!this.isDragging) return;
    
    const y = e.clientY - this.dragOffset.y;
    
    // 限制在视窗内，保持在左侧
    const maxY = window.innerHeight - this.trigger.offsetHeight;
    const constrainedY = Math.max(0, Math.min(y, maxY));
    
    this.trigger.style.top = constrainedY + 'px';
    this.trigger.style.transform = 'translateY(0)';
  };

  /**
   * 处理拖拽结束
   */
  handleDragEnd = () => {
    this.isDragging = false;
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.handleDragEnd);
    
    // 恢复居中变换，但保持新的top位置
    setTimeout(() => {
      if (!this.isDragging) {
        this.trigger.style.transform = 'translateY(-50%)';
      }
    }, 100);
  };

  /**
   * 切换面板显示
   */
  togglePanel() {
    if (!this.floatingPanel) {
      // 延迟加载浮层面板
      this.initFloatingPanel();
    }
    
    if (this.floatingPanel) {
      this.floatingPanel.toggle();
    }
  }

  /**
   * 初始化浮层面板
   */
  initFloatingPanel() {
    try {
      if (window.FloatingPanel) {
        this.floatingPanel = new window.FloatingPanel();
        console.log('✅ 浮层面板已创建');
      } else {
        console.error('❌ FloatingPanel 类未找到');
      }
    } catch (error) {
      console.error('❌ 创建浮层面板失败:', error);
    }
  }

  /**
   * 显示触发器
   */
  show() {
    this.trigger.style.display = 'flex';
    this.isVisible = true;
  }

  /**
   * 隐藏触发器
   */
  hide() {
    this.trigger.style.display = 'none';
    this.isVisible = false;
  }

  /**
   * 销毁触发器
   */
  destroy() {
    if (this.trigger && this.trigger.parentNode) {
      this.trigger.parentNode.removeChild(this.trigger);
    }
    if (this.floatingPanel) {
      this.floatingPanel.hide();
    }
  }
}

// 导出到全局作用域
window.FloatingTrigger = FloatingTrigger;

// 自动初始化逻辑
(function() {
  // 防止重复初始化
  if (window.floatingTriggerInstance) {
    console.log('🔄 浮层触发器已存在，跳过重复初始化');
    return;
  }

  function initializeTrigger() {
    try {
      console.log('🚀 开始初始化浮层触发器...');
      window.floatingTriggerInstance = new FloatingTrigger();
      console.log('✅ 自动化浮层触发器已启动');
    } catch (error) {
      console.error('❌ 初始化浮层触发器失败:', error);
    }
  }

  // 确保DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTrigger);
  } else {
    // DOM已加载，延迟一点时间确保页面稳定
    setTimeout(initializeTrigger, 100);
  }
})();
