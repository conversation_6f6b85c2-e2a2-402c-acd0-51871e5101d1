﻿{
  "manifest_version": 3,
  "name": "Universal Automation Assistant",
  "version": "2.0.0",
  "description": "Universal web automation system",
  "permissions": ["activeTab", "tabs", "scripting", "storage"],
  "host_permissions": ["<all_urls>"],
  "action": {
    "default_popup": "plugin-automation-popup.html",
    "default_icon": {
      "16": "icons/icon16.png",
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    }
  },
  "background": {
    "service_worker": "background/background.js"
  },
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content/content-modular.js"],
      "run_at": "document_start"
    },
    {
      "matches": ["<all_urls>"],
      "js": ["floating-automation.js"],
      "run_at": "document_end"
    }
  ],
  "icons": {
    "16": "icons/icon16.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  },
  "web_accessible_resources": [
    {
      "resources": [
        "universal-automation-engine.js",
        "workflow-manager.js",
        "workflow-designer.html",
        "workflow-designer.js",
        "workflow-designer-cytoscape.js",
        "workflow-designer-mxgraph.html",
        "workflow-designer-mxgraph.js",
        "js/logic-flow.js",
        "js/mxClient.min.js",
        "content/content.js",
        "modules/content/content-core.js",
        "modules/content/sensitive-word-detector.js",
        "modules/content/content-automation.js",
        "modules/designer/designer-core.js",
        "modules/designer/designer-nodes.js",
        "modules/designer/designer-workflow.js",
        "floating-panel.js",
        "floating-trigger.js",
        "floating-automation.js"
      ],
      "matches": ["<all_urls>"]
    }
  ]
}
