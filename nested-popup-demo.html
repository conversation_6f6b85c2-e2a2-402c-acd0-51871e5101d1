<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三层嵌套弹窗自动化测试Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #2d3436;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #636e72;
            font-size: 16px;
        }

        .flow-description {
            background: #e17055;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-weight: bold;
        }

        .main-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .list-header {
            background: #0984e3;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }

        .virtual-list-viewport {
            height: 600px;
            overflow-y: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            position: relative;
        }

        .virtual-list-content {
            position: relative;
        }

        .list-item {
            height: 80px;
            border-bottom: 1px solid #eee;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: white;
            transition: background-color 0.2s;
            position: absolute;
            width: 100%;
            left: 0;
        }

        .list-item:hover {
            background: #f8f9fa;
        }

        .list-item.pending-item {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .item-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .item-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #0984e3;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .item-details h4 {
            margin: 0;
            color: #2d3436;
            font-size: 16px;
        }

        .item-details p {
            margin: 5px 0 0 0;
            color: #636e72;
            font-size: 14px;
        }

        .pending-badge {
            background: #ffc107;
            color: #856404;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .processed-badge {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .list-item.processed-item {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .list-item.processed-item .test-btn {
            background: #6c757d;
            cursor: not-allowed;
        }

        .list-item.processed-item .test-btn:hover {
            background: #6c757d;
            transform: none;
        }

        .test-btn {
            background: #00b894;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.2s;
        }

        .test-btn:hover {
            background: #00a085;
            transform: translateY(-1px);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal {
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .modal-header {
            background: #0984e3;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: 18px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .modal-footer {
            padding: 20px 30px;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.2s;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-primary {
            background: #0984e3;
            color: white;
        }

        .btn-primary:hover {
            background: #0770c4;
        }

        .btn:disabled {
            background: #e9ecef !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0984e3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            text-align: center;
            color: #636e72;
            margin-top: 15px;
        }

        /* 多选列表样式 */
        .selection-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 15px 0;
        }

        .selection-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .selection-item:last-child {
            border-bottom: none;
        }

        .selection-item:hover {
            background: #f8f9fa;
        }

        .selection-item.selected {
            background: #e3f2fd;
            border-left: 4px solid #0984e3;
        }

        .selection-item.disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .selection-item.disabled:hover {
            background: #f8f9fa;
        }

        .selection-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .selection-item.selected .selection-checkbox {
            background: #0984e3;
            border-color: #0984e3;
            color: white;
        }

        .selection-preview {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #0984e3;
        }

        .confirmation-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .confirmation-summary h4 {
            margin: 0 0 15px 0;
            color: #2d3436;
        }

        .confirmation-summary p {
            margin: 8px 0;
            color: #636e72;
        }

        .warning-text {
            color: #e17055;
            font-weight: bold;
            margin-top: 15px;
            padding: 10px;
            background: #ffeaa7;
            border-radius: 6px;
        }

        /* 滚动条样式 */
        .virtual-list-viewport::-webkit-scrollbar,
        .selection-list::-webkit-scrollbar {
            width: 8px;
        }

        .virtual-list-viewport::-webkit-scrollbar-track,
        .selection-list::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .virtual-list-viewport::-webkit-scrollbar-thumb,
        .selection-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .virtual-list-viewport::-webkit-scrollbar-thumb:hover,
        .selection-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 三层嵌套弹窗自动化测试Demo</h1>
        <p>专为浏览器插件自动化测试设计的独立Demo页面</p>
        <div class="flow-description">
            主列表 → 弹窗1（智能等待）→ 弹窗2（多选列表）→ 弹窗3（最终确认）→ 自动返回
        </div>
    </div>

    <div class="main-container">
        <div class="list-header">
            📋 主页面数据列表 - 自动化测试目标
        </div>
        
        <div class="virtual-list-viewport" id="virtualListViewport">
            <div class="virtual-list-content" id="virtualListContent">
                <!-- 虚拟列表项将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 动态弹窗容器 - 所有弹窗都将在这里动态创建 -->
    <div id="dynamicModalContainer"></div>

    <script>
        // 虚拟滚动列表实现
        class VirtualList {
            constructor(container, options) {
                this.viewport = container.querySelector('#virtualListViewport');
                this.content = container.querySelector('#virtualListContent');

                this.itemHeight = options.itemHeight || 80;
                this.visibleCount = options.visibleCount || 20;
                this.totalCount = options.totalCount || 100;
                this.buffer = options.buffer || 5;

                this.startIndex = 0;
                this.endIndex = this.visibleCount;

                this.data = this.generateData();
                this.init();
            }

            generateData() {
                const data = [];
                const pendingIndices = new Set();

                // 随机选择30%的项目设为"待处理"状态
                const pendingCount = Math.floor(this.totalCount * 0.3);
                while (pendingIndices.size < pendingCount) {
                    pendingIndices.add(Math.floor(Math.random() * this.totalCount));
                }

                for (let i = 0; i < this.totalCount; i++) {
                    const isPending = pendingIndices.has(i);
                    data.push({
                        id: i + 1,
                        title: `数据项目 #${String(i + 1).padStart(3, '0')}`,
                        description: `第${i + 1}个测试数据项目，${isPending ? '包含复杂的选择逻辑' : '包含完整的弹窗流程'}`,
                        pending: isPending,
                        processed: false
                    });
                }

                return data;
            }

            init() {
                this.content.style.height = `${this.totalCount * this.itemHeight}px`;
                this.viewport.addEventListener('scroll', () => this.handleScroll());
                this.render();
            }

            handleScroll() {
                const scrollTop = this.viewport.scrollTop;
                const newStartIndex = Math.floor(scrollTop / this.itemHeight);
                const newEndIndex = Math.min(
                    newStartIndex + this.visibleCount + this.buffer,
                    this.totalCount
                );

                if (newStartIndex !== this.startIndex || newEndIndex !== this.endIndex) {
                    this.startIndex = Math.max(0, newStartIndex - this.buffer);
                    this.endIndex = newEndIndex;
                    this.render();
                }
            }

            render() {
                this.content.innerHTML = '';

                for (let i = this.startIndex; i < this.endIndex; i++) {
                    if (i >= this.totalCount) break;

                    const item = this.data[i];
                    const element = this.createItemElement(item, i);
                    this.content.appendChild(element);
                }
            }

            createItemElement(item, index) {
                const element = document.createElement('div');
                let className = 'list-item';
                if (item.processed) {
                    className += ' processed-item';
                } else if (item.pending) {
                    className += ' pending-item';
                }
                element.className = className;
                element.style.top = `${index * this.itemHeight}px`;
                element.setAttribute('data-item-id', item.id);
                element.setAttribute('data-item-index', index);

                let badgeHtml = '';
                if (item.processed) {
                    badgeHtml = '<span class="processed-badge">已处理</span>';
                } else if (item.pending) {
                    badgeHtml = '<span class="pending-badge">待处理</span>';
                }

                const buttonDisabled = item.processed ? 'disabled' : '';
                const buttonText = item.processed ? '已完成' : '测试按钮';
                const onclickAttr = item.processed ? '' : `onclick="startNestedFlow(${item.id}, ${item.pending})"`;

                element.innerHTML = `
                    <div class="item-info">
                        <div class="item-icon">#${String(item.id).padStart(3, '0')}</div>
                        <div class="item-details">
                            <h4>${item.title}</h4>
                            <p>${item.description}</p>
                        </div>
                        ${badgeHtml}
                    </div>
                    <button class="test-btn" ${buttonDisabled} ${onclickAttr}>
                        ${buttonText}
                    </button>
                `;

                return element;
            }
        }

        // 三层嵌套弹窗管理
        let currentFlow = null;
        let loadingTimer = null;
        let modal2LoadingTimer = null;
        let modal3LoadingTimer = null;
        let selectedItems = new Set();
        let disabledItems = new Set();
        let modalContainer = null;

        // 动态创建弹窗的工具函数
        function createModalElement(modalId, title, content, footer) {
            const overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.id = modalId;
            overlay.style.display = 'flex';

            overlay.innerHTML = `
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title">${title}</h3>
                        <button class="close-btn" onclick="closeModal('${modalId}')">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        ${footer}
                    </div>
                </div>
            `;

            // 点击空白区域关闭弹窗
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeModal(modalId);
                }
            });

            return overlay;
        }

        // 移除弹窗元素
        function removeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }

        function startNestedFlow(itemId, isPending) {
            currentFlow = {
                itemId: itemId,
                isPending: isPending,
                selectedItems: new Set(),
                currentStep: 1
            };

            selectedItems.clear();
            openModal1();
        }

        function openModal1() {
            console.log('🔧 [DEBUG] 开始动态创建第一层弹窗');

            const modalId = 'modal1Overlay';
            const title = `数据项目 #${String(currentFlow.itemId).padStart(3, '0')} - 智能等待`;

            const content = `
                <div style="text-align: center;">
                    <div class="loading-spinner"></div>
                    <div class="loading-text" id="loadingText">智能等待中，模拟网络请求...</div>
                </div>
            `;

            const footer = `
                <button class="btn btn-secondary" onclick="closeModal('${modalId}')">取消</button>
                <button class="btn btn-primary" id="modal1NextBtn" onclick="openModal2()" disabled>加载中...</button>
            `;

            // 动态创建并添加弹窗
            const modal = createModalElement(modalId, title, content, footer);
            modalContainer.appendChild(modal);

            console.log('🔧 [DEBUG] 第一层弹窗DOM已创建并添加到页面');

            // 获取动态创建的元素
            const nextBtn = document.getElementById('modal1NextBtn');
            const loadingText = document.getElementById('loadingText');

            // 模拟智能等待过程
            let countdown = 3;
            loadingText.textContent = `智能等待中，模拟网络请求... ${countdown}秒`;

            loadingTimer = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    loadingText.textContent = `智能等待中，模拟网络请求... ${countdown}秒`;
                } else {
                    loadingText.textContent = '加载完成！可以进入下一步';
                    nextBtn.disabled = false;
                    nextBtn.textContent = '下一步';
                    clearInterval(loadingTimer);
                    console.log('🔧 [DEBUG] 第一层弹窗加载完成');
                }
            }, 1000);
        }

        function openModal2() {
            console.log('🔧 [DEBUG] 关闭第一层弹窗，开始创建第二层弹窗');

            // 移除第一层弹窗
            removeModal('modal1Overlay');
            if (loadingTimer) {
                clearInterval(loadingTimer);
            }

            const modalId = 'modal2Overlay';
            const title = `数据项目 #${String(currentFlow.itemId).padStart(3, '0')} - 多选列表`;

            const content = `
                <div id="modal2LoadingState">
                    <div style="text-align: center;">
                        <div class="loading-spinner"></div>
                        <div class="loading-text" id="modal2LoadingText">正在加载选择项...</div>
                    </div>
                </div>
                <div id="modal2ContentState" style="display: none;">
                    <p>请选择要处理的数据项（可多选）：</p>
                    <div class="selection-list" id="selectionList">
                        <!-- 多选项将在这里动态生成 -->
                    </div>
                    <div class="selection-preview" id="selectionPreview">
                        已选择 0 项数据
                    </div>
                </div>
            `;

            const footer = `
                <button class="btn btn-secondary" onclick="closeModal('${modalId}')">返回</button>
                <button class="btn btn-primary" id="modal2ConfirmBtn" onclick="openModal3()" disabled>确认选择</button>
            `;

            // 动态创建并添加弹窗
            const modal = createModalElement(modalId, title, content, footer);
            modalContainer.appendChild(modal);

            console.log('🔧 [DEBUG] 第二层弹窗DOM已创建并添加到页面');

            // 获取动态创建的元素
            const loadingState = document.getElementById('modal2LoadingState');
            const contentState = document.getElementById('modal2ContentState');
            const confirmBtn = document.getElementById('modal2ConfirmBtn');
            const loadingText = document.getElementById('modal2LoadingText');

            // 重置状态
            selectedItems.clear();
            disabledItems.clear();

            // 模拟异步加载选择项
            let countdown = 2;
            loadingText.textContent = `正在加载选择项... ${countdown}秒`;

            modal2LoadingTimer = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    loadingText.textContent = `正在加载选择项... ${countdown}秒`;
                } else {
                    loadingText.textContent = '选择项加载完成！';

                    // 生成多选列表
                    generateSelectionList();
                    updateSelectionPreview();

                    // 显示内容，隐藏加载
                    loadingState.style.display = 'none';
                    contentState.style.display = 'block';
                    confirmBtn.disabled = false;

                    clearInterval(modal2LoadingTimer);
                    console.log('🔧 [DEBUG] 第二层弹窗加载完成');
                }
            }, 1000);
        }

        function generateSelectionList() {
            const selectionList = document.getElementById('selectionList');
            selectionList.innerHTML = '';

            // 随机选择30%的项目设为禁用
            const totalItems = 8;
            const disabledCount = Math.floor(totalItems * 0.3);
            disabledItems.clear();

            while (disabledItems.size < disabledCount) {
                disabledItems.add(Math.floor(Math.random() * totalItems) + 1);
            }

            // 生成8个选择项
            for (let i = 1; i <= 8; i++) {
                const isDisabled = disabledItems.has(i);
                const item = document.createElement('div');
                item.className = `selection-item ${isDisabled ? 'disabled' : ''}`;
                item.setAttribute('data-value', i);

                if (!isDisabled) {
                    item.onclick = () => toggleSelection(i);
                }

                item.innerHTML = `
                    <div class="selection-checkbox">
                        <span style="display: none;">✓</span>
                    </div>
                    <div>
                        <strong>选项 ${i}</strong> - 数据处理模块 ${i}
                        ${isDisabled ? '<span style="color: #dc3545; font-size: 12px;">(已禁用)</span>' : ''}
                    </div>
                `;

                selectionList.appendChild(item);
            }

            // 默认不选择任何项目
            selectedItems.clear();
        }

        function toggleSelection(value) {
            // 检查是否被禁用
            if (disabledItems.has(value)) {
                return;
            }

            const item = document.querySelector(`[data-value="${value}"]`);
            const checkbox = item.querySelector('.selection-checkbox span');

            if (selectedItems.has(value)) {
                selectedItems.delete(value);
                item.classList.remove('selected');
                checkbox.style.display = 'none';
            } else {
                selectedItems.add(value);
                item.classList.add('selected');
                checkbox.style.display = 'block';
            }

            updateSelectionPreview();
        }

        function updateSelectionPreview() {
            const preview = document.getElementById('selectionPreview');
            const count = selectedItems.size;
            const items = Array.from(selectedItems).sort((a, b) => a - b);

            if (count === 0) {
                preview.textContent = '已选择 0 项数据';
            } else {
                preview.textContent = `已选择 ${count} 项数据：选项 ${items.join(', ')}`;
            }

            currentFlow.selectedItems = new Set(selectedItems);
        }

        function openModal3() {
            console.log('🔧 [DEBUG] 关闭第二层弹窗，开始创建第三层弹窗');

            // 移除第二层弹窗
            removeModal('modal2Overlay');
            if (modal2LoadingTimer) {
                clearInterval(modal2LoadingTimer);
            }

            const modalId = 'modal3Overlay';
            const title = `数据项目 #${String(currentFlow.itemId).padStart(3, '0')} - 最终确认`;

            const content = `
                <div id="modal3LoadingState">
                    <div style="text-align: center;">
                        <div class="loading-spinner"></div>
                        <div class="loading-text" id="modal3LoadingText">正在生成操作摘要...</div>
                    </div>
                </div>
                <div id="modal3ContentState" style="display: none;">
                    <p>即将执行以下操作：</p>
                    <div class="confirmation-summary" id="confirmationSummary">
                        <!-- 确认信息将在这里动态生成 -->
                    </div>
                    <div class="warning-text" id="warningText" style="display: none;">
                        ⚠️ 注意：此操作的确定按钮已被禁用用于测试
                    </div>
                </div>
            `;

            const footer = `
                <button class="btn btn-secondary" onclick="closeModal('${modalId}')">返回</button>
                <button class="btn btn-primary" id="modal3ExecuteBtn" onclick="executeAction()" disabled>确定</button>
            `;

            // 动态创建并添加弹窗
            const modal = createModalElement(modalId, title, content, footer);
            modalContainer.appendChild(modal);

            console.log('🔧 [DEBUG] 第三层弹窗DOM已创建并添加到页面');

            // 获取动态创建的元素
            const loadingState = document.getElementById('modal3LoadingState');
            const contentState = document.getElementById('modal3ContentState');
            const executeBtn = document.getElementById('modal3ExecuteBtn');
            const loadingText = document.getElementById('modal3LoadingText');

            // 保存当前选择的项目
            currentFlow.selectedItems = new Set(selectedItems);

            // 模拟异步生成操作摘要
            let countdown = 2;
            loadingText.textContent = `正在生成操作摘要... ${countdown}秒`;

            modal3LoadingTimer = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    loadingText.textContent = `正在生成操作摘要... ${countdown}秒`;
                } else {
                    loadingText.textContent = '操作摘要生成完成！';

                    // 生成确认内容
                    generateConfirmationSummary();

                    // 显示内容，隐藏加载
                    loadingState.style.display = 'none';
                    contentState.style.display = 'block';

                    // 设置按钮状态
                    executeBtn.disabled = currentFlow.isPending;

                    clearInterval(modal3LoadingTimer);
                    console.log('🔧 [DEBUG] 第三层弹窗加载完成');
                }
            }, 1000);
        }

        function generateConfirmationSummary() {
            const summary = document.getElementById('confirmationSummary');
            const warning = document.getElementById('warningText');

            const selectedArray = Array.from(currentFlow.selectedItems).sort((a, b) => a - b);
            const disabledArray = Array.from(disabledItems).sort((a, b) => a - b);

            summary.innerHTML = `
                <h4>操作摘要：</h4>
                <p><strong>目标项目：</strong>数据项目 #${String(currentFlow.itemId).padStart(3, '0')}</p>
                <p><strong>选择项目：</strong>${selectedArray.length > 0 ? `选项 ${selectedArray.join(', ')} (共${selectedArray.length}项)` : '未选择任何项目'}</p>
                <p><strong>禁用项目：</strong>${disabledArray.length > 0 ? `选项 ${disabledArray.join(', ')} (共${disabledArray.length}项)` : '无禁用项目'}</p>
                <p><strong>操作类型：</strong>${currentFlow.isPending ? '复杂选择逻辑处理' : '标准数据处理'}</p>
                <p><strong>预计耗时：</strong>约 ${Math.max(selectedArray.length * 0.5, 0.5)} 秒</p>
            `;

            if (currentFlow.isPending) {
                warning.style.display = 'block';
            } else {
                warning.style.display = 'none';
            }
        }

        function executeAction() {
            console.log('🔧 [DEBUG] 执行操作');
            // 标记当前项目为已处理
            markItemAsProcessed(currentFlow.itemId);
            // 直接关闭所有弹窗，不显示alert
            closeAllModals();
        }

        function closeModal(modalId) {
            console.log('🔧 [DEBUG] 关闭弹窗:', modalId);

            // 清理定时器
            if (modalId === 'modal1Overlay' && loadingTimer) {
                clearInterval(loadingTimer);
                loadingTimer = null;
            } else if (modalId === 'modal2Overlay' && modal2LoadingTimer) {
                clearInterval(modal2LoadingTimer);
                modal2LoadingTimer = null;
            } else if (modalId === 'modal3Overlay' && modal3LoadingTimer) {
                clearInterval(modal3LoadingTimer);
                modal3LoadingTimer = null;
            }

            // 第三层弹窗关闭时，标记为已处理
            if (modalId === 'modal3Overlay' && currentFlow && currentFlow.itemId) {
                markItemAsProcessed(currentFlow.itemId);
            }

            // 移除弹窗DOM元素
            removeModal(modalId);

            // 第一层弹窗关闭时，重置流程状态
            if (modalId === 'modal1Overlay') {
                currentFlow = null;
                selectedItems.clear();
                disabledItems.clear();
            }
        }

        function closeAllModals() {
            console.log('🔧 [DEBUG] 关闭所有弹窗');

            // 移除所有可能存在的弹窗
            removeModal('modal1Overlay');
            removeModal('modal2Overlay');
            removeModal('modal3Overlay');

            // 清理所有定时器
            if (loadingTimer) {
                clearInterval(loadingTimer);
                loadingTimer = null;
            }
            if (modal2LoadingTimer) {
                clearInterval(modal2LoadingTimer);
                modal2LoadingTimer = null;
            }
            if (modal3LoadingTimer) {
                clearInterval(modal3LoadingTimer);
                modal3LoadingTimer = null;
            }

            // 重置状态
            currentFlow = null;
            selectedItems.clear();
            disabledItems.clear();
        }

        // 标记项目为已处理
        function markItemAsProcessed(itemId) {
            // 更新数据模型
            const dataItem = virtualListInstance.data.find(item => item.id === itemId);
            if (dataItem) {
                dataItem.processed = true;
            }

            // 更新DOM显示
            const listItem = document.querySelector(`[data-item-id="${itemId}"]`);
            if (listItem) {
                // 更新样式类
                listItem.className = 'list-item processed-item';

                // 更新标签
                const badge = listItem.querySelector('.pending-badge, .processed-badge');
                if (badge) {
                    badge.className = 'processed-badge';
                    badge.textContent = '已处理';
                }

                // 更新按钮
                const button = listItem.querySelector('.test-btn');
                if (button) {
                    button.disabled = true;
                    button.textContent = '已完成';
                    button.removeAttribute('onclick');
                }
            }
        }

        // 全局虚拟列表实例引用
        let virtualListInstance = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化动态弹窗容器
            modalContainer = document.getElementById('dynamicModalContainer');
            if (!modalContainer) {
                console.error('❌ 动态弹窗容器未找到');
                return;
            }
            console.log('✅ 动态弹窗容器已初始化');

            const container = document.querySelector('.main-container');
            virtualListInstance = new VirtualList(container, {
                itemHeight: 80,
                visibleCount: 20,
                totalCount: 100,
                buffer: 5
            });

            // // 点击空白区域关闭弹窗
            // ['modal1Overlay', 'modal2Overlay', 'modal3Overlay'].forEach((id, index) => {
            //     document.getElementById(id).addEventListener('click', function(e) {
            //         if (e.target === this) {
            //             closeModal(index + 1);
            //         }
            //     });
            // });
        });
    </script>
</body>
</html>
